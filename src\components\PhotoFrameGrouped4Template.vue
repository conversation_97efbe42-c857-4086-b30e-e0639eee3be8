<template>
  <!-- Grouped4 Template Component - Hidden by default, used for cloning (PHOTOS4 Mode) - Text-only -->
  <div id="photo-frame-template-grouped4" class="photo-frame-grouped" style="display: none" ref="templateRef">
    <!-- Text content area (no image, following PHOTOS2 pattern) -->
    <div class="photo-content-grouped">
      <div class="groupeddwad1">1你喜欢的建筑</div>
      <div class="groupeddwad2">一年级</div>
      <div class="groupeddwad3">
        <div class="groupeddwad31">参与人：</div>
        <div class="groupeddwad32 groupeddwad3211">34名</div>
      </div>
      <div class="groupeddwad3">
        <div class="groupeddwad31">作品数：</div>
        <div class="groupeddwad32 groupeddwad321">234件</div>
      </div>
    </div>

    <!-- Focus mode navigation buttons (hidden by default) -->
    <div class="focus-nav-buttons">
      <button class="focus-nav-btn enter-btn">进入</button>
      <button class="focus-nav-btn return-btn">返回</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineExpose } from "vue";

// Template reference for external access
const templateRef = ref(null);

// Props for component configuration
const props = defineProps({
  // Allow customization of template ID if needed
  templateId: {
    type: String,
    default: "photo-frame-template-grouped4",
  },
  // Allow visibility control from parent
  visible: {
    type: Boolean,
    default: false,
  },
});

// Update template ID and visibility when component mounts
onMounted(() => {
  if (templateRef.value) {
    // Set custom template ID if provided
    if (props.templateId !== "photo-frame-template-grouped4") {
      templateRef.value.id = props.templateId;
    }

    // Update visibility based on props
    if (props.visible) {
      templateRef.value.style.display = "";
    }
  } else {
    console.warn("⚠️ Template reference not available in PhotoFrameGrouped4Template");
  }
});

// Expose template reference and utility methods for parent access
defineExpose({
  templateRef,
  getTemplateElement: () => templateRef.value,
  setVisible: (visible) => {
    if (templateRef.value) {
      templateRef.value.style.display = visible ? "" : "none";
    }
  },
  getId: () => templateRef.value?.id || props.templateId,
});
</script>

<style scoped>
/* ===== GROUPED4 TEMPLATE STYLES (PHOTOS4 MODE) - Text-only, minimal styling ===== */
.photo-frame-grouped {
  width: 322px;
  height: 226px;
  cursor: pointer;
  position: relative;
  /* Optimize for 3D transforms - enables hardware acceleration */
  will-change: transform;
  background-image: url("../../public/x.png");
  background-repeat: no-repeat;
  background-size: contain;

  display: flex;
  justify-content: center;
  align-items: center;
}

/* Hover effects removed for text-only PHOTOS4 mode */

/* Category badge styling removed for text-only PHOTOS4 mode */

/* Number overlay styling removed for text-only PHOTOS4 mode */

/* Image styles removed for text-only PHOTOS4 mode */

/* Content container for grouped template - text-only layout */

/* Focus navigation buttons within grouped photo frame */
.photo-frame-grouped .focus-nav-buttons {
  position: absolute;
  bottom: -64px;
  left: 50%;
  transform: translateX(-50%);
  display: none; /* Hidden by default */
  flex-direction: row;
  gap: 15px;
  z-index: 1001;
  pointer-events: auto;
  /* Ensure buttons stay within parent bounds */
  max-width: calc(100% - 30px); /* Account for left/right margins */
  justify-content: center;
}

/* Show buttons only when element is focused */
.photo-frame-grouped.focus-centered .focus-nav-buttons {
  display: flex;
}

/* Focus navigation button styles */
.focus-nav-btn {
  background: linear-gradient(135deg, rgba(100, 150, 255, 0.9) 0%, rgba(80, 120, 200, 0.9) 100%);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  min-width: 60px;
  text-align: center;
  user-select: none;
  /* Ensure buttons are clickable */
  pointer-events: auto;
  z-index: 1002;
}

.focus-nav-btn:hover {
  background: linear-gradient(135deg, rgba(120, 170, 255, 1) 0%, rgba(100, 140, 220, 1) 100%);
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 6px 20px rgba(100, 150, 255, 0.4);
  transform: translateY(-2px);
}

.focus-nav-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.4);
}

/* Specific styles for enter and return buttons */
.focus-nav-btn.enter-btn {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.9) 0%, rgba(56, 142, 60, 0.9) 100%);
}

.focus-nav-btn.enter-btn:hover {
  background: linear-gradient(135deg, rgba(102, 187, 106, 1) 0%, rgba(76, 175, 80, 1) 100%);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.focus-nav-btn.return-btn {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.9) 0%, rgba(245, 124, 0, 0.9) 100%);
}

.focus-nav-btn.return-btn:hover {
  background: linear-gradient(135deg, rgba(255, 183, 77, 1) 0%, rgba(255, 152, 0, 1) 100%);
  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
}

/* Instant positioning for grouped template */
.photo-frame-grouped.instant-position {
  transition: none !important;
}

.photo-frame-grouped.instant-position * {
  transition: none !important;
}

/* Fly-in animation for grouped template */
.photo-frame-grouped.fly-in-active {
  transition: none !important;
}

.photo-content-grouped {
  width: 64%;
  height: 64%;

  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
.groupeddwad1 {
  width: 100%;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: black;
  font-size: 25px;
  font-weight: bold;
  letter-spacing: 1px;
}
.groupeddwad2 {
  margin-top: 5px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #734c03;
  font-weight: bold;
  font-size: 22px;
  letter-spacing: 3px;
}
.groupeddwad3 {
  margin-top: 5px;
  width: 100%;
  display: flex;
  align-items: center;
  margin-left: 91px;
}
.groupeddwad31 {
  color: #8a6103;
  letter-spacing: 1px;
  font-size: 20px;
}
.groupeddwad32 {
  font-weight: bold;
  letter-spacing: 1px;
  font-size: 20px;
}
</style>
