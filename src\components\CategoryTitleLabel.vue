<template>
  <!-- Category Title Label Component - Hidden by default, used for cloning (PHOTOS4 Mode) -->
  <div id="category-title-label-template" class="category-title-label" style="display: none" ref="templateRef">
    <div class="category-title-text2">三年级</div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineExpose } from "vue";

// Template reference for external access
const templateRef = ref(null);

// Props for component configuration
const props = defineProps({
  // Allow customization of template ID if needed
  templateId: {
    type: String,
    default: "category-title-label-template",
  },
  // Allow visibility control from parent
  visible: {
    type: Boolean,
    default: false,
  },
});

// Update template ID and visibility when component mounts
onMounted(() => {
  if (templateRef.value) {
    // Set custom template ID if provided
    if (props.templateId !== "category-title-label-template") {
      templateRef.value.id = props.templateId;
    }

    // Update visibility based on props
    if (props.visible) {
      templateRef.value.style.display = "";
    }
  } else {
    console.warn("⚠️ Template reference not available in CategoryTitleLabel");
  }
});

// Expose template reference and utility methods for parent access
defineExpose({
  templateRef,
  getTemplateElement: () => templateRef.value,
  setVisible: (visible) => {
    if (templateRef.value) {
      templateRef.value.style.display = visible ? "" : "none";
    }
  },
  getId: () => templateRef.value?.id || props.templateId,
});
</script>

<style scoped>
/* ===== CATEGORY TITLE LABEL STYLES (PHOTOS4 MODE) ===== */
.category-title-label {
  position: absolute;
  width: auto;
  height: auto;
  width: 320px;
  height: 68px;
  border-radius: 15px;
  cursor: default;
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* Add containment for child elements */
  contain: layout style;
  /* Optimize for 3D transforms - enables hardware acceleration */
  will-change: transform;
  /* Enhanced hardware acceleration for smooth rightward movement animations */
  transform-style: preserve-3d;
  backface-visibility: hidden;
  padding: 16px 24px;
  text-align: center;
  z-index: 10;

  background-image: url("../../public/s.png");
  background-repeat: no-repeat;
  background-size: contain;
}
.category-title-text2 {
  width: 100%;
  height: 90%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40px;
  font-weight: bold;
  letter-spacing: 3px;
}

/* Title text for category label */
.category-title-text {
  font-size: 20px;
  font-weight: bold;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
  margin-bottom: 8px;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Decorative underline for category label */
.category-title-underline {
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, rgba(100, 150, 255, 0.8) 0%, rgba(80, 120, 200, 0.8) 50%, rgba(100, 150, 255, 0.8) 100%);
  border-radius: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
  transition: all 0.3s ease;
}

.category-title-label:hover .category-title-underline {
  background: linear-gradient(90deg, rgba(120, 170, 255, 1) 0%, rgba(100, 140, 220, 1) 50%, rgba(120, 170, 255, 1) 100%);
  box-shadow: 0 2px 6px rgba(100, 150, 255, 0.4);
  transform: scaleX(1.05);
}

/* Category-specific title colors */
.category-title-label[data-category="wildlife"] .category-title-text {
  color: rgba(255, 193, 7, 1); /* Golden yellow for wildlife */
}

.category-title-label[data-category="wildlife"] .category-title-underline {
  background: linear-gradient(90deg, rgba(255, 193, 7, 0.8) 0%, rgba(255, 152, 0, 0.8) 50%, rgba(255, 193, 7, 0.8) 100%);
}

.category-title-label[data-category="space"] .category-title-text {
  color: rgba(138, 43, 226, 1); /* Purple for space */
}

.category-title-label[data-category="space"] .category-title-underline {
  background: linear-gradient(90deg, rgba(138, 43, 226, 0.8) 0%, rgba(75, 0, 130, 0.8) 50%, rgba(138, 43, 226, 0.8) 100%);
}

.category-title-label[data-category="cuisine"] .category-title-text {
  color: rgba(255, 87, 34, 1); /* Orange-red for cuisine */
}

.category-title-label[data-category="cuisine"] .category-title-underline {
  background: linear-gradient(90deg, rgba(255, 87, 34, 0.8) 0%, rgba(255, 69, 0, 0.8) 50%, rgba(255, 87, 34, 0.8) 100%);
}

.category-title-label[data-category="sports"] .category-title-text {
  color: rgba(76, 175, 80, 1); /* Green for sports */
}

.category-title-label[data-category="sports"] .category-title-underline {
  background: linear-gradient(90deg, rgba(76, 175, 80, 0.8) 0%, rgba(56, 142, 60, 0.8) 50%, rgba(76, 175, 80, 0.8) 100%);
}

/* Focus mode styles for category title label */
.category-title-label.focus-fade-out {
  opacity: 0.1 !important;
  transition: opacity 0.4s ease !important;
}

.category-title-label.focus-centered {
  opacity: 1 !important;
  z-index: 1000;
  transition: opacity 0.4s ease !important;
  box-shadow: 0 20px 60px rgba(100, 150, 255, 0.4) !important;
  border-color: rgba(100, 150, 255, 0.9) !important;
}

/* Instant positioning for category title label */
.category-title-label.instant-position {
  transition: none !important;
}

.category-title-label.instant-position * {
  transition: none !important;
}

/* Fly-in animation for category title label */
.category-title-label.fly-in-active {
  transition: none !important;
}
</style>
