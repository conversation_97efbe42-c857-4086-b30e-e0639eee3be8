<template>
  <!-- Standard Template Component - Hidden by default, used for cloning -->
  <div id="photo-frame-template" class="photo-frame" style="display: none" ref="templateRef">
    <div class="photo-number-overlay"></div>
    <img class="photo-image" alt="" />
    <div class="photo-title"></div>
    <div class="photo-description"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineExpose } from "vue";

// Template reference for external access
const templateRef = ref(null);

// Props for component configuration
const props = defineProps({
  // Allow customization of template ID if needed
  templateId: {
    type: String,
    default: "photo-frame-template",
  },
  // Allow visibility control from parent
  visible: {
    type: Boolean,
    default: false,
  },
});

// Emits for component events
const emit = defineEmits(["template-ready", "template-error"]);

// Update template ID and visibility when component mounts
onMounted(() => {
  if (templateRef.value) {
    // Set custom template ID if provided
    if (props.templateId !== "photo-frame-template") {
      templateRef.value.id = props.templateId;
    }

    // Update visibility based on props
    if (props.visible) {
      templateRef.value.style.display = "";
    }

    // Emit ready event with template reference
    emit("template-ready", templateRef.value);

    console.log(`📋 Standard Template Component mounted with ID: ${templateRef.value.id}`);
  } else {
    console.error("❌ Standard Template Component: Failed to get template reference");
    emit("template-error", "Failed to get template reference");
  }
});

// Expose template reference and utility methods for parent access
defineExpose({
  templateRef,
  getTemplateElement: () => templateRef.value,
  setVisible: (visible) => {
    if (templateRef.value) {
      templateRef.value.style.display = visible ? "" : "none";
    }
  },
  getId: () => templateRef.value?.id || props.templateId,
});
</script>

<style scoped>
/* Global styles for CSS3D elements */
.photo-frame {
  width: 300px;
  height: 400px;
  background: rgba(0, 0, 0, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  cursor: pointer;
  /* Default: smooth transitions for hover effects only */
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
  overflow: hidden;
  backdrop-filter: blur(10px);
  /* Optimize for 3D transforms - enables hardware acceleration */
  will-change: transform;
}

/* Layout-specific transition rules */
/* RANDOM layout: PERFORMANCE OPTIMIZED for 60 FPS - Minimal transitions */
.photo-frame.layout-random {
  /* PERFORMANCE: Only animate transform for hardware acceleration */
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* Enhanced hardware acceleration for smooth animations */
  transform-style: preserve-3d;
  backface-visibility: hidden;
  /* PERFORMANCE: will-change managed dynamically via JavaScript */
}

/* RANDOM2 layout: PERFORMANCE OPTIMIZED for 60 FPS - Faster transitions for more dynamic feel */
.photo-frame.layout-random2 {
  /* PERFORMANCE: Faster transitions for RANDOM2's more dynamic nature */
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* Enhanced hardware acceleration for smooth animations */
  transform-style: preserve-3d;
  backface-visibility: visible; /* FIXED: Allow backface to be visible during Y-axis rotation */
  /* PERFORMANCE: will-change managed dynamically via JavaScript */
}

/* GROUPED layout: Enable smooth transitions for dataset switching */
.photo-frame.layout-grouped {
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* GROUPED layout fly-in animation: Disable transitions during fly-in for better control */
.photo-frame.layout-grouped.fly-in-active {
  transition: none !important;
}

/* GRID layout: Disable transform transitions to prevent animated wrap-around */
.photo-frame.layout-grid {
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
}

/* GRID layout fly-in animation: Disable transitions during fly-in for better control */
.photo-frame.layout-grid.fly-in-active {
  transition: none !important;
}

/* Layout switching: Smooth transitions when switching between layouts */
.photo-frame.layout-switching {
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.photo-frame:hover {
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.7);
  transform: scale(1.05);
}

/* Enhanced hover effects for RANDOM layout in standard template - PERFORMANCE OPTIMIZED for 60 FPS */
.photo-frame.layout-random:hover {
  /* PERFORMANCE: Simplified hover effects using only transform and border */
  border-color: rgba(255, 255, 255, 0.8);
  border-width: 3px;
  transform: scale(1.02);
}

/* PERFORMANCE OPTIMIZED random layout styling for standard template - 60 FPS target */
.photo-frame.layout-random {
  /* PERFORMANCE: Removed expensive box-shadow and backdrop-filter for better performance */
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(0, 0, 0, 0.9);
}

/* PERFORMANCE OPTIMIZED RANDOM2 layout styling for standard template - 60 FPS target */
.photo-frame.layout-random2 {
  /* PERFORMANCE: Distinct styling with purple/magenta theme */
  border: 2px solid rgba(255, 100, 255, 0.4) !important;
  background: rgba(20, 0, 20, 0.9) !important;
}

/* Enhanced hover effects for RANDOM2 layout in standard template - PERFORMANCE OPTIMIZED for 60 FPS */
.photo-frame.layout-random2:hover {
  /* PERFORMANCE: Distinct hover effects for RANDOM2 */
  border-color: rgba(255, 100, 255, 0.9);
  border-width: 3px;
  transform: scale(1.03);
}

.photo-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 8px 8px 0 0;
}

.photo-title {
  position: absolute;
  bottom: 60px;
  left: 15px;
  right: 15px;
  color: white;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.photo-description {
  position: absolute;
  bottom: 15px;
  left: 15px;
  right: 15px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.photo-number-overlay {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  z-index: 10;
  opacity: 0.8;
  transition: opacity 0.3s ease, background 0.3s ease;
}

/* Show number overlay more prominently on hover */
.photo-frame:hover .photo-number-overlay {
  opacity: 1;
  background: rgba(0, 0, 0, 0.9);
}

/* Class to completely disable transitions during wrap-around for instant positioning */
.photo-frame.instant-position {
  transition: none !important;
}

.photo-frame.instant-position * {
  transition: none !important;
}

/* Focus mode styles - active in RANDOM and PHOTOS2 layouts */
.photo-frame.focus-fade-out {
  opacity: 0.1 !important;
  transition: opacity 0.4s ease !important;
  cursor: not-allowed !important; /* Show not-allowed cursor for non-focused elements */
}

.photo-frame.focus-centered {
  opacity: 1 !important;
  z-index: 1000; /* Bring focused element to front */
  transition: opacity 0.4s ease !important;
  box-shadow: 0 20px 60px rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(255, 255, 255, 0.8) !important;
  cursor: pointer !important; /* Keep pointer cursor for focused element */
}
</style>
