import * as THREE from "three";
import { CSS3DObject } from "three/examples/jsm/renderers/CSS3DRenderer.js";

/**
 * LayoutManager - 管理CSS3D照片画廊的网格和随机布局功能
 *
 * 此模块处理：
 * - 具有连续水平滚动和环绕行为的网格布局
 * - 具有3D定位和浮动动画的随机布局
 * - 具有y轴旋转和增强动画的RANDOM2布局
 * - 基于列的分组布局，支持统一速度的水平滚动
 * - 随机模式、RANDOM2模式和分组模式下的点击聚焦功能
 * - 视口检测和智能动画控制
 * - 平滑布局过渡
 * - 可配置的滚动速度设置
 */
export class LayoutManager {
  constructor(objects, camera, renderCallback, focusCallbacks = {}, scene = null) {
    this.objects = objects; // CSS3D对象数组
    this.camera = camera; // THREE.Camera实例
    this.renderCallback = renderCallback; // 触发重新渲染的函数
    this.focusCallbacks = focusCallbacks; // 聚焦模式回调函数 { onEnterFocus, onExitFocus }
    this.scene = scene; // THREE.Scene实例（用于添加category title labels）

    // 布局目标存储
    this.targets = { grid: [], flipGrid: [], random: [], random2: [], grouped: [] };

    // Category title objects for PHOTOS4 mode
    this.categoryTitleObjects = [];
    this.categoryTitleTargets = [];
    this.lastGroupData = null;
    this.lastGlobalTopY = 0;
    this.lastRowHeight = 0;

    // Category title objects for PHOTOS2 mode
    this.categoryTitle2Objects = [];
    this.categoryTitle2Targets = [];
    this.categoryTitle2ScrollAnimationData = [];
    this.isCategoryTitle2ScrollingEnabled = false;

    // 当前布局状态
    this.currentLayout = "grid";
    this.animationStartTime = 0;

    // PHOTOS1, PHOTOS2, PHOTOS3, PHOTOS4 and PHOTOS5 mode detection and uniform speed settings
    this.currentDataset = null; // Store current dataset reference for mode detection
    this.isPhotos1Mode = false; // Flag to track if we're in PHOTOS1 mode
    this.isPhotos2Mode = false; // Flag to track if we're in PHOTOS2 mode
    this.isPhotos3Mode = false; // Flag to track if we're in PHOTOS3 mode
    this.isPhotos4Mode = false; // Flag to track if we're in PHOTOS4 mode
    this.isPhotos5Mode = false; // Flag to track if we're in PHOTOS5 mode
    this.photos3UniformSpeed = 1.8; // Uniform speed for all rows in PHOTOS3 mode
    this.photos5UniformSpeed = 1.6; // Uniform speed for all rows in PHOTOS5 mode

    // 随机布局动画属性 - ENHANCED for dramatic visual impact
    this.randomAnimationData = []; // 每个元素的动画数据
    this.lastRandomUpdate = 0; // 上次随机位置更新时间
    this.randomUpdateInterval = 15000; // ENHANCED: 4秒更新间隔 for more frequent dramatic changes

    // 瀑布雨布局动画属性 - Waterfall rain effect
    this.random2AnimationData = []; // 瀑布雨模式的动画数据
    this.lastRandom2Update = 0; // 上次瀑布雨重置时间
    this.random2UpdateInterval = 30000; // 瀑布雨: 30秒重置间隔，让雨滴有足够时间完成循环

    // Random layout animation parameters
    this.floatingAmplitude = 80; // Floating animation amplitude (pixels)
    this.rotationAmplitude = 0.15; // Rotation animation amplitude (radians)
    this.scaleAmplitude = 0.12; // Scale pulsing amplitude (scale factor)
    this.driftAmplitude = 60; // X/Z axis drift amplitude (pixels)

    // PERFORMANCE OPTIMIZATION: Pre-computed animation lookup tables
    this.precomputedSines = []; // Pre-computed sine values for floating animation
    this.precomputedCosines = []; // Pre-computed cosine values for rotation animation
    this.animationLookupSize = 1440; // Number of pre-computed values for smooth animation

    // 网格布局滚动动画属性
    this.gridScrollAnimationData = []; // 网格滚动动画数据
    this.flipGridScrollAnimationData = []; // 翻转网格滚动动画数据（垂直方向）

    // 分组布局滚动动画属性
    this.groupedScrollAnimationData = []; // 分组滚动动画数据
    this.isGroupedScrollingEnabled = false; // 是否启用分组滚动动画
    this.groupedLayoutBounds = { totalWidth: 0, viewportWidth: 0 }; // 分组布局尺寸
    this.groupedColumnScrollSpeed = 1.5; // 分组布局列滚动的统一速度（可配置）

    // Category title header scroll animation properties for PHOTOS4 mode
    this.categoryTitleScrollAnimationData = []; // Category title header scroll animation data
    this.isCategoryTitleScrollingEnabled = false; // Whether category title scrolling is enabled

    // PHOTOS2飞入动画属性
    this.groupedFlyInAnimationData = []; // 分组布局飞入动画数据
    this.isGroupedFlyInActive = false; // 是否正在执行飞入动画
    this.groupedFlyInStartTime = 0; // 飞入动画开始时间
    this.groupedFlyInDuration = 1500; // 飞入动画持续时间（1.5秒，更明显）
    this.groupedFlyInBatchSize = 6; // 每批处理的元素数量（减少批次大小）
    this.groupedFlyInBatchDelay = 150; // 批次间延迟（增加延迟使效果更明显）

    // PHOTOS4飞入动画属性（使用与PHOTOS2相同的参数）
    this.grouped4FlyInAnimationData = []; // PHOTOS4分组布局飞入动画数据
    this.isGrouped4FlyInActive = false; // 是否正在执行PHOTOS4飞入动画
    this.grouped4FlyInStartTime = 0; // PHOTOS4飞入动画开始时间

    // 网格飞入动画属性（适用于PHOTOS1、PHOTOS3和PHOTOS5模式）
    this.gridFlyInAnimationData = []; // 网格布局飞入动画数据
    this.isGridFlyInActive = false; // 是否正在执行网格飞入动画
    this.gridFlyInStartTime = 0; // 网格飞入动画开始时间
    this.gridFlyInDuration = 1000; // 网格飞入动画持续时间（1.0秒，在0.6-1.2秒范围内）
    this.gridFlyInBatchSize = 6; // 每批处理的元素数量（6-8范围内）
    this.gridFlyInBatchDelay = 150; // 批次间延迟（150-200ms范围内）

    // 视口检测属性
    this.viewportCheckInterval = 500; // 视口检查间隔（毫秒）
    this.lastViewportCheck = 0; // 上次视口检查时间
    this.isScrollingEnabled = true; // 是否启用滚动动画
    this.viewportBounds = { left: 0, right: 0, top: 0, bottom: 0 }; // 视口边界

    // 部分可见性检测配置
    this.partialVisibilityThreshold = 0.4; // 40%可见性阈值（0.25-0.5范围）
    this.viewportBufferMultiplier = 0.05; // 5%视口缓冲区以提高检测精度

    // 点击聚焦属性（仅随机模式）
    this.focusedObject = null; // 当前聚焦的对象
    this.isFocusMode = false; // 是否处于聚焦模式
    this.isExitingFocus = false; // 是否正在退出聚焦模式（防止重复退出）
    this.isFocusAnimationComplete = false; // 聚焦进入动画是否完成
    this.pendingFocusExit = false; // 是否有待处理的聚焦退出请求
    this.originalPositions = []; // 存储原始位置以便恢复
    this.animationsPaused = false; // 动画是否暂停

    // 初始化布局
    this.setupLayoutTargets();

    // 设置键盘事件监听器
    this.setupKeyboardListeners();

    // ENHANCED ANIMATIONS: Pre-compute animation lookup tables for 60 FPS performance
    this.initializeAnimationLookupTables();
  }

  /**
   * 初始化网格、翻转网格、随机和RANDOM2布局目标
   */
  setupLayoutTargets() {
    this.setupGridLayout();
    this.setupFlipGridLayout();
    this.generateRandomLayout();
    this.generateRandom2Layout();
  }

  /**
   * 设置当前数据集并检测PHOTOS1、PHOTOS2、PHOTOS3、PHOTOS4和PHOTOS5模式
   * @param {Array} dataset - 当前数据集
   * @param {string} modeHint - 模式提示（可选）
   */
  setCurrentDataset(dataset, modeHint = null) {
    this.currentDataset = dataset;

    // 如果提供了明确的模式提示，优先使用
    if (modeHint) {
      this.isPhotos1Mode = modeHint === "photos1";
      this.isPhotos2Mode = modeHint === "photos2";
      this.isPhotos3Mode = modeHint === "photos3";
      this.isPhotos4Mode = modeHint === "photos4";
      this.isPhotos5Mode = modeHint === "photos5";
      console.log(`🎯 Mode explicitly set to: ${modeHint.toUpperCase()}`);
    } else {
      // 否则使用自动检测
      this.isPhotos1Mode = this.detectPhotos1Mode(dataset);
      this.isPhotos2Mode = this.detectPhotos2Mode(dataset);
      this.isPhotos3Mode = this.detectPhotos3Mode(dataset);
      this.isPhotos4Mode = this.detectPhotos4Mode(dataset);
      this.isPhotos5Mode = this.detectPhotos5Mode(dataset);
    }

    // 如果是PHOTOS1模式，启用点击外部退出聚焦行为
    if (this.isPhotos1Mode) {
      console.log(`🎯 PHOTOS1 Mode: Enabled click-outside-to-exit focus behavior`);
    }

    // 如果是PHOTOS2模式，启用特殊的聚焦行为
    if (this.isPhotos2Mode) {
      console.log(`🎯 PHOTOS2 Mode: Enabled sticky focus behavior for text-only content`);
    }

    // 如果是PHOTOS4模式，启用特殊的聚焦行为
    if (this.isPhotos4Mode) {
      console.log(`🎯 PHOTOS4 Mode: Enabled sticky focus behavior for text-only content`);
    }

    // 如果是PHOTOS3模式，设置统一的滚动速度
    if (this.isPhotos3Mode) {
      // 为分组布局也设置统一速度
      this.setGroupedColumnScrollSpeed(this.photos3UniformSpeed);
      console.log(`🎯 PHOTOS3 Mode: Set uniform speed ${this.photos3UniformSpeed} for both grid and grouped layouts`);
    }

    // 如果是PHOTOS5模式，设置统一的滚动速度
    if (this.isPhotos5Mode) {
      console.log(`🎯 PHOTOS5 Mode: Set uniform speed ${this.photos5UniformSpeed} for all 3 rows in grid layout`);
    }

    console.log(`🎯 LayoutManager: Dataset updated, PHOTOS3 mode: ${this.isPhotos3Mode}, PHOTOS5 mode: ${this.isPhotos5Mode}`);
  }

  /**
   * Set category title label template reference for PHOTOS4 mode
   * @param {HTMLElement} template - The category title label template element
   */
  setCategoryTitleLabelTemplate(template) {
    this.categoryTitleLabelTemplate = template;
  }

  /**
   * Set category title label template reference for PHOTOS2 mode
   * @param {HTMLElement} template - The category title label2 template element
   */
  setCategoryTitleLabel2Template(template) {
    this.categoryTitleLabel2Template = template;
  }

  /**
   * Create category title labels for PHOTOS4 mode
   * @param {Array} groupData - Group data from setupGroupedLayout
   * @param {number} globalTopY - Global top Y position for alignment
   * @param {number} rowHeight - Height between rows
   */
  createCategoryTitleLabels(groupData, globalTopY, rowHeight) {
    // Clear existing category title objects
    this.clearCategoryTitleLabels();

    if (!this.categoryTitleLabelTemplate) {
      console.warn("⚠️ Category title label template not available");
      return;
    }

    groupData.forEach((group, groupIndex) => {
      try {
        // Clone the template element
        const element = this.categoryTitleLabelTemplate.cloneNode(true);
        if (!element) {
          throw new Error(`Failed to clone category title label template for group ${groupIndex}`);
        }

        // Make element visible and give it a unique ID
        element.style.display = "";
        element.id = `category-title-${groupIndex}`;

        // Set the title text
        const titleText = element.querySelector(".category-title-text");
        if (titleText && group.categoryGroup.title) {
          titleText.textContent = group.categoryGroup.title;
        }

        // Set category-specific styling
        if (group.categoryGroup.category) {
          element.setAttribute("data-category", group.categoryGroup.category);
        }

        // Calculate title position (above the group)
        const titleY = globalTopY + rowHeight * 0.8; // Position above the top row
        const titleX = group.groupCenterX; // Center horizontally with the group
        const titleZ = -400; // Same Z as photos for consistent alignment

        // Create CSS3D object for the title
        const titleObject = new CSS3DObject(element);
        titleObject.position.set(titleX, titleY, titleZ);
        titleObject.rotation.set(0, 0, 0);

        // Store title object data
        titleObject.userData = {
          category: group.categoryGroup.category,
          categoryTitle: group.categoryGroup.title,
          groupIndex: group.groupIndex,
          isCategoryTitle: true,
        };

        // Add to scene and store reference
        this.categoryTitleObjects.push(titleObject);
        this.categoryTitleTargets.push({
          position: { x: titleX, y: titleY, z: titleZ },
          rotation: { x: 0, y: 0, z: 0 },
        });

        // Add to the scene (assuming we have access to the scene)
        if (this.scene) {
          this.scene.add(titleObject);
        }

        console.log(`✅ Created category title label for group ${groupIndex}: "${group.categoryGroup.title}"`);
      } catch (error) {
        console.error(`❌ Failed to create category title label for group ${groupIndex}:`, error);
      }
    });
  }

  /**
   * Update category title labels positions with rightward scroll animation
   */
  updateCategoryTitleLabels() {
    // If category title scrolling is enabled and we have animation data, apply scroll animation
    if (this.isCategoryTitleScrollingEnabled && this.categoryTitleScrollAnimationData.length > 0) {
      this.updateCategoryTitleScrollAnimation();
    } else {
      // Static positioning when scrolling is disabled
      for (let i = 0; i < this.categoryTitleObjects.length; i++) {
        const titleObject = this.categoryTitleObjects[i];
        const target = this.categoryTitleTargets[i];

        if (titleObject && target) {
          // Keep title labels in their static positions
          titleObject.position.copy(target.position);
          titleObject.scale.set(1, 1, 1);
          titleObject.rotation.set(0, 0, 0);
        }
      }
    }
  }

  /**
   * Clear all category title labels and their scroll animation data
   */
  clearCategoryTitleLabels() {
    this.categoryTitleObjects.forEach((titleObject) => {
      if (this.scene && titleObject.parent) {
        this.scene.remove(titleObject);
      }
      // Clean up DOM element
      if (titleObject.element && titleObject.element.parentNode) {
        titleObject.element.parentNode.removeChild(titleObject.element);
      }
    });
    this.categoryTitleObjects = [];
    this.categoryTitleTargets = [];

    // Clear category title scroll animation data
    this.categoryTitleScrollAnimationData = [];
    this.isCategoryTitleScrollingEnabled = false;
  }

  /**
   * Create category title labels for PHOTOS2 mode
   * @param {Array} groupData - Group data from setupGroupedLayout
   * @param {number} globalTopY - Global top Y position for alignment
   * @param {number} rowHeight - Height between rows
   */
  createCategoryTitle2Labels(groupData, globalTopY, rowHeight) {
    // Clear existing category title2 objects
    this.clearCategoryTitle2Labels();

    if (!this.categoryTitleLabel2Template) {
      console.warn("⚠️ Category title label2 template not available");
      return;
    }

    groupData.forEach((group, groupIndex) => {
      try {
        // Clone the template element
        const element = this.categoryTitleLabel2Template.cloneNode(true);
        if (!element) {
          throw new Error(`Failed to clone category title label2 template for group ${groupIndex}`);
        }

        // Make element visible and give it a unique ID
        element.style.display = "";
        element.id = `category-title2-${groupIndex}`;

        // Set the title text
        const titleText = element.querySelector(".category-title-text2");
        if (titleText && group.categoryGroup.title) {
          titleText.textContent = group.categoryGroup.title;
        }

        // Set category-specific styling
        if (group.categoryGroup.category) {
          element.setAttribute("data-category", group.categoryGroup.category);
        }

        // Calculate title position (above the group)
        const titleY = globalTopY + rowHeight * 0.8; // Position above the top row
        const titleX = group.groupCenterX; // Center horizontally with the group
        const titleZ = -400; // Same Z as photos for consistent alignment

        // Create CSS3D object for the title
        const titleObject = new CSS3DObject(element);
        titleObject.position.set(titleX, titleY, titleZ);
        titleObject.rotation.set(0, 0, 0);

        // Store title object data
        titleObject.userData = {
          category: group.categoryGroup.category,
          categoryTitle: group.categoryGroup.title,
          groupIndex: group.groupIndex,
          isCategoryTitle2: true,
        };

        // Add to scene and store reference
        this.categoryTitle2Objects.push(titleObject);
        this.categoryTitle2Targets.push({
          position: { x: titleX, y: titleY, z: titleZ },
          rotation: { x: 0, y: 0, z: 0 },
        });

        // Add to the scene (assuming we have access to the scene)
        if (this.scene) {
          this.scene.add(titleObject);
        }

        console.log(`✅ Created category title2 label for group ${groupIndex}: "${group.categoryGroup.title}"`);
      } catch (error) {
        console.error(`❌ Failed to create category title2 label for group ${groupIndex}:`, error);
      }
    });
  }

  /**
   * Update category title2 labels positions with rightward scroll animation
   */
  updateCategoryTitle2Labels() {
    // If category title2 scrolling is enabled and we have animation data, apply scroll animation
    if (this.isCategoryTitle2ScrollingEnabled && this.categoryTitle2ScrollAnimationData.length > 0) {
      this.updateCategoryTitle2ScrollAnimation();
    } else {
      // Static positioning when scrolling is disabled
      for (let i = 0; i < this.categoryTitle2Objects.length; i++) {
        const titleObject = this.categoryTitle2Objects[i];
        const target = this.categoryTitle2Targets[i];

        if (titleObject && target) {
          // Keep title labels in their static positions
          titleObject.position.copy(target.position);
          titleObject.scale.set(1, 1, 1);
          titleObject.rotation.set(0, 0, 0);
        }
      }
    }
  }

  /**
   * Clear all category title2 labels and their scroll animation data
   */
  clearCategoryTitle2Labels() {
    this.categoryTitle2Objects.forEach((titleObject) => {
      if (this.scene && titleObject.parent) {
        this.scene.remove(titleObject);
      }
      // Clean up DOM element
      if (titleObject.element && titleObject.element.parentNode) {
        titleObject.element.parentNode.removeChild(titleObject.element);
      }
    });
    this.categoryTitle2Objects = [];
    this.categoryTitle2Targets = [];

    // Clear category title2 scroll animation data
    this.categoryTitle2ScrollAnimationData = [];
    this.isCategoryTitle2ScrollingEnabled = false;
  }

  /**
   * 检测是否为PHOTOS3模式
   * @param {Array} dataset - 要检测的数据集
   * @returns {boolean} 是否为PHOTOS3模式
   */
  detectPhotos3Mode(dataset) {
    if (!dataset || !Array.isArray(dataset) || dataset.length === 0) {
      return false;
    }

    // 检查数据集是否具有PHOTOS3的特征
    // PHOTOS3数据集的特征：扁平结构，包含特定的ID模式和内容
    const hasPhotos3Characteristics = dataset.some(
      (item) =>
        item &&
        typeof item === "object" &&
        item.hasOwnProperty("src") &&
        item.hasOwnProperty("title") &&
        item.hasOwnProperty("description") &&
        !item.hasOwnProperty("category") && // PHOTOS3不包含category属性
        item.title &&
        (item.title.includes("Urban") ||
          item.title.includes("Technology") ||
          item.title.includes("Digital") ||
          item.title.includes("Smart") ||
          item.title.includes("Future") ||
          item.title.includes("Vintage") ||
          item.title.includes("Street") ||
          item.title.includes("Coffee") ||
          item.title.includes("Industrial") ||
          item.title.includes("Neon") ||
          item.title.includes("Retro") ||
          item.title.includes("Cyber") ||
          item.title.includes("Electric") ||
          item.title.includes("Holographic") ||
          item.title.includes("Quantum") ||
          item.title.includes("Neural") ||
          item.title.includes("Synthetic") ||
          item.title.includes("Virtual") ||
          item.title.includes("Augmented") ||
          item.title.includes("Transport"))
    );

    // Additional check: look for PHOTOS3-specific URL patterns (random=2xx range)
    const hasPhotos3UrlPattern = dataset.some(
      (item) =>
        item &&
        item.src &&
        item.src.includes("random=2") && // PHOTOS3 uses random=201-230 range
        (item.src.includes("random=20") || item.src.includes("random=21") || item.src.includes("random=22") || item.src.includes("random=23"))
    );

    return hasPhotos3Characteristics || hasPhotos3UrlPattern;
  }

  /**
   * 检测数据集是否为PHOTOS4模式
   * @param {Array} dataset - 要检测的数据集
   * @returns {boolean} - 如果是PHOTOS4数据集则返回true
   */
  detectPhotos4Mode(dataset) {
    if (!dataset || !Array.isArray(dataset) || dataset.length === 0) {
      return false;
    }

    // 检查数据集是否具有PHOTOS4的特征
    // PHOTOS4数据集的特征：分类结构，包含category属性和photos数组，文本内容（无src字段）
    const hasPhotos4Characteristics = dataset.some(
      (item) =>
        item &&
        typeof item === "object" &&
        item.hasOwnProperty("category") && // PHOTOS4包含category属性
        item.hasOwnProperty("title") &&
        item.hasOwnProperty("photos") && // PHOTOS4包含photos数组
        Array.isArray(item.photos) &&
        !item.hasOwnProperty("src") && // PHOTOS4是文本模式，不包含src
        item.photos.length > 0 &&
        item.photos.some(
          (photo) => photo && typeof photo === "object" && photo.hasOwnProperty("title") && photo.hasOwnProperty("description") && !photo.hasOwnProperty("src") // 照片项也不包含src字段
        )
    );

    // Additional check: look for PHOTOS4-specific category patterns
    const hasPhotos4CategoryPattern = dataset.some(
      (item) =>
        item &&
        item.category &&
        (item.category.includes("wildlife") ||
          item.category.includes("architecture") ||
          item.category.includes("nature") ||
          item.category.includes("urban") ||
          item.category.includes("abstract") ||
          item.category.includes("portrait") ||
          item.category.includes("landscape") ||
          item.category.includes("technology"))
    );

    return hasPhotos4Characteristics || hasPhotos4CategoryPattern;
  }

  /**
   * 检测是否为PHOTOS5模式
   * @param {Array} dataset - 要检测的数据集
   * @returns {boolean} 是否为PHOTOS5模式
   */
  detectPhotos5Mode(dataset) {
    if (!dataset || !Array.isArray(dataset) || dataset.length === 0) {
      return false;
    }

    // 检查数据集是否具有PHOTOS5的特征
    // PHOTOS5数据集的特征：扁平结构，包含特定的字段
    const hasPhotos5Characteristics = dataset.some(
      (item) =>
        item &&
        typeof item === "object" &&
        item.hasOwnProperty("src") &&
        item.hasOwnProperty("title") &&
        item.hasOwnProperty("description") &&
        !item.hasOwnProperty("category") && // PHOTOS5不包含category属性
        // 检查是否包含PHOTOS5特有的字段（如nianji, banji等）
        (item.hasOwnProperty("nianji") ||
          item.hasOwnProperty("banji") ||
          item.hasOwnProperty("student_name") ||
          item.hasOwnProperty("like") ||
          item.hasOwnProperty("praise") ||
          // 或者检查特定的关键词
          (item.title &&
            (item.title.includes("Landscape") ||
              item.title.includes("Mountain") ||
              item.title.includes("Ocean") ||
              item.title.includes("Forest") ||
              item.title.includes("Desert") ||
              item.title.includes("Valley") ||
              item.title.includes("Lake") ||
              item.title.includes("River") ||
              item.title.includes("Sunset") ||
              item.title.includes("Sunrise") ||
              item.title.includes("Cloud") ||
              item.title.includes("Sky") ||
              item.title.includes("Nature") ||
              item.title.includes("Scenic") ||
              item.title.includes("Vista") ||
              item.title.includes("Horizon") ||
              item.title.includes("Peak") ||
              item.title.includes("Coast") ||
              item.title.includes("Beach") ||
              item.title.includes("Wilderness"))))
    );

    // Additional check: look for PHOTOS5-specific URL patterns (random=3xx range)
    const hasPhotos5UrlPattern = dataset.some(
      (item) =>
        item &&
        item.src &&
        item.src.includes("random=3") && // PHOTOS5 uses random=301-350 range
        (item.src.includes("random=30") ||
          item.src.includes("random=31") ||
          item.src.includes("random=32") ||
          item.src.includes("random=33") ||
          item.src.includes("random=34") ||
          item.src.includes("random=35"))
    );

    return hasPhotos5Characteristics || hasPhotos5UrlPattern;
  }

  /**
   * 检测是否为PHOTOS1模式
   * @param {Array} dataset - 要检测的数据集
   * @returns {boolean} 是否为PHOTOS1模式
   */
  detectPhotos1Mode(dataset) {
    if (!dataset || !Array.isArray(dataset) || dataset.length === 0) {
      return false;
    }

    // PHOTOS1数据集的特征：
    // 1. 扁平结构，包含src字段（图像模式）
    // 2. 包含title和description字段
    // 3. 不包含category字段（与PHOTOS2/PHOTOS4区分）
    // 4. 不包含PHOTOS3/PHOTOS5特有的关键词
    const hasPhotos1Characteristics = dataset.some(
      (item) =>
        item &&
        typeof item === "object" &&
        item.hasOwnProperty("src") &&
        item.hasOwnProperty("title") &&
        item.hasOwnProperty("description") &&
        !item.hasOwnProperty("category") && // PHOTOS1不包含category属性
        !item.hasOwnProperty("photos") && // PHOTOS1不包含photos数组
        item.src && // 确保有图像源
        item.title &&
        // 排除PHOTOS3特有的关键词
        !this.hasPhotos3Keywords(item.title) &&
        // 排除PHOTOS5特有的关键词
        !this.hasPhotos5Keywords(item.title)
    );

    // Additional check: look for PHOTOS1-specific URL patterns (random=1xx range)
    const hasPhotos1UrlPattern = dataset.some(
      (item) =>
        item &&
        item.src &&
        (item.src.includes("random=1") || // PHOTOS1 uses random=101-200 range
          item.src.includes("random=10") ||
          item.src.includes("random=11") ||
          item.src.includes("random=12") ||
          item.src.includes("random=13") ||
          item.src.includes("random=14") ||
          item.src.includes("random=15") ||
          item.src.includes("random=16") ||
          item.src.includes("random=17") ||
          item.src.includes("random=18") ||
          item.src.includes("random=19"))
    );

    return hasPhotos1Characteristics || hasPhotos1UrlPattern;
  }

  /**
   * 检查标题是否包含PHOTOS3特有的关键词
   * @param {string} title - 要检查的标题
   * @returns {boolean} 是否包含PHOTOS3关键词
   */
  hasPhotos3Keywords(title) {
    if (!title) return false;

    const photos3Keywords = [
      "Urban",
      "Technology",
      "Digital",
      "Smart",
      "Future",
      "Vintage",
      "Street",
      "Coffee",
      "Industrial",
      "Neon",
      "Retro",
      "Cyber",
      "Electric",
      "Holographic",
      "Quantum",
      "Neural",
      "Synthetic",
      "Virtual",
      "Augmented",
      "Transport",
    ];

    return photos3Keywords.some((keyword) => title.includes(keyword));
  }

  /**
   * 检查标题是否包含PHOTOS5特有的关键词
   * @param {string} title - 要检查的标题
   * @returns {boolean} 是否包含PHOTOS5关键词
   */
  hasPhotos5Keywords(title) {
    if (!title) return false;

    const photos5Keywords = [
      "Landscape",
      "Mountain",
      "Ocean",
      "Forest",
      "Desert",
      "Valley",
      "Lake",
      "River",
      "Sunset",
      "Sunrise",
      "Cloud",
      "Sky",
      "Nature",
      "Scenic",
      "Vista",
      "Horizon",
      "Peak",
      "Coast",
      "Beach",
      "Wilderness",
    ];

    return photos5Keywords.some((keyword) => title.includes(keyword));
  }

  /**
   * 检测是否为PHOTOS2模式
   * @param {Array} dataset - 要检测的数据集
   * @returns {boolean} 是否为PHOTOS2模式
   */
  detectPhotos2Mode(dataset) {
    if (!dataset || !Array.isArray(dataset) || dataset.length === 0) {
      return false;
    }

    // PHOTOS2数据集的特征：
    // 1. 嵌套结构，包含category和photos数组
    // 2. 内部photos项目没有src字段（text-only）
    // 3. 包含title和description字段
    // 4. 包含category字段
    const hasPhotos2Structure = dataset.some(
      (item) =>
        item &&
        typeof item === "object" &&
        item.hasOwnProperty("category") &&
        item.hasOwnProperty("title") &&
        item.hasOwnProperty("photos") &&
        Array.isArray(item.photos) &&
        item.photos.length > 0 &&
        // Check that photos items don't have src (text-only)
        item.photos.some(
          (photo) =>
            photo &&
            typeof photo === "object" &&
            !photo.hasOwnProperty("src") && // No src field for text-only content
            photo.hasOwnProperty("title") &&
            photo.hasOwnProperty("description")
        )
    );

    // Additional check: look for PHOTOS2-specific content patterns
    const hasPhotos2ContentPattern = dataset.some(
      (item) =>
        item &&
        item.title &&
        (item.title.includes("年级") || // Grade levels like "三年级", "四年级"
          item.title.includes("特长班") || // Special classes
          item.category === "nature" ||
          item.category === "architecture" ||
          item.category === "arts")
    );

    return hasPhotos2Structure && hasPhotos2ContentPattern;
  }

  /**
   * 动态生成行设置：根据行数生成Y位置和滚动速度
   * @param {number} rows - 行数
   * @returns {Array} 行设置数组，包含每行的Y位置和滚动速度
   */
  generateRowSettings(rows) {
    const rowSettings = [];

    // 配置不同行数布局的间距参数
    const layoutConfigs = {
      3: {
        rowSpacing: 450, // 3行布局：行间距450px（保持向后兼容）
        centerY: 0, // 中心Y位置
        baseSpeed: 1.5, // 基础速度
        speedVariation: 0.5, // 速度变化范围
        speedPattern: [-2.0, 1.5, -2.5], // 第一行和第三行向左(-)，第二行向右(+)
        directionPattern: [-1, 1, -1], // 第一行和第三行向左，第二行向右
      },
      5: {
        rowSpacing: 300, // 5行布局：行间距200px（更紧凑的垂直分布）
        centerY: 0, // 中心Y位置
        baseSpeed: 1.5, // 基础速度
        speedVariation: 0.3, // 速度变化范围（较小变化以保持一致性）
        speedPattern: [-2.0, 1.5, -2.0, 1.5, -2.0], // 奇数行向左，偶数行向右
        directionPattern: [-1, 1, -1, 1, -1], // 奇数行向左，偶数行向右
      },
      generic: {
        rowSpacing: 180, // 通用布局：行间距180px（适合多行布局）
        centerY: 0, // 中心Y位置
        baseSpeed: 1.4, // 基础速度
        speedVariation: 0.4, // 速度变化范围
        speedPattern: null, // 使用动态计算
        directionPattern: null, // 使用动态计算
      },
    };

    // 选择配置：优先使用特定行数配置，否则使用通用配置
    const config = layoutConfigs[rows] || layoutConfigs.generic;

    for (let i = 0; i < rows; i++) {
      // 计算Y位置：从中心点向上下均匀分布
      const y = config.centerY + (i - (rows - 1) / 2) * config.rowSpacing;

      let speed;
      let direction = 1; // 声明direction变量，默认值为1（向右）

      // PHOTOS3模式：所有行使用统一速度
      if (this.isPhotos3Mode) {
        speed = this.photos3UniformSpeed;
        // 为PHOTOS3模式设置统一向右方向（所有行都向右移动）
        direction = 1; // 所有行都向右移动
        if (i === 0) {
          console.log(`🎯 PHOTOS3 Uniform Speed: All ${rows} rows set to uniform speed ${speed} with rightward direction`);
        }
      } else if (this.isPhotos5Mode) {
        // PHOTOS5模式：所有行使用统一速度
        speed = this.photos5UniformSpeed;
        // 为PHOTOS5模式设置统一向右方向（所有行都向右移动）
        direction = 1; // 所有行都向右移动
        if (i === 0) {
          console.log(`🎯 PHOTOS5 Uniform Speed: All ${rows} rows set to uniform speed ${speed} with rightward direction`);
        }
      } else if (config.speedPattern && config.speedPattern.length === rows) {
        // 使用预定义的速度模式（如3行布局）
        speed = Math.abs(config.speedPattern[i]); // 取绝对值作为速度
        direction = config.speedPattern[i] >= 0 ? 1 : -1; // 根据正负号确定方向
      } else if (config.directionPattern && config.directionPattern.length === rows) {
        // 使用预定义的方向模式
        direction = config.directionPattern[i];
        // 动态计算速度：中间行较慢，边缘行较快
        const distanceFromCenter = Math.abs(i - (rows - 1) / 2);
        const maxDistance = (rows - 1) / 2;
        const normalizedDistance = maxDistance > 0 ? distanceFromCenter / maxDistance : 0;
        speed = config.baseSpeed + normalizedDistance * config.speedVariation;
      } else {
        // 动态计算速度和方向：奇数行向左，偶数行向右
        direction = i % 2 === 0 ? -1 : 1; // 第0行(第一行)向左，第1行(第二行)向右，以此类推
        const distanceFromCenter = Math.abs(i - (rows - 1) / 2);
        const maxDistance = (rows - 1) / 2;
        const normalizedDistance = maxDistance > 0 ? distanceFromCenter / maxDistance : 0;
        speed = config.baseSpeed + normalizedDistance * config.speedVariation;
      }

      // 确保速度在合理范围内
      speed = Math.max(0.8, Math.min(3.0, speed));

      // 应用方向到速度
      const finalSpeed = speed * direction;

      rowSettings.push({
        y: Math.round(y), // 四舍五入到整数像素
        speed: Math.round(finalSpeed * 10) / 10, // 保留一位小数，包含方向
      });
    }

    return rowSettings;
  }

  /**
   * 设置具有可配置行数和水平滚动的网格布局
   * @param {number} rowCount - 网格行数（默认为3）
   */
  setupGridLayout(rowCount = 3) {
    // 清除现有的网格布局和动画数据
    this.targets.grid = [];
    this.gridScrollAnimationData = [];

    const rows = rowCount;
    const cols = Math.ceil(this.objects.length / rows); // 计算指定行数所需的列数

    // PHOTOS3和PHOTOS5模式使用更紧凑的列间距
    let columnSpacing = 650; // 默认列间距
    if (this.isPhotos3Mode) {
      columnSpacing = 450; // PHOTOS3模式：缩短列间距，让照片排列更紧凑
      console.log(`🎯 PHOTOS3 Mode: Using compact column spacing ${columnSpacing}px`);
    } else if (this.isPhotos5Mode) {
      columnSpacing = 700; // PHOTOS5模式：更大的列间距，让3行布局有宽松的视觉间距
      console.log(`🎯 PHOTOS5 Mode: Using spacious column spacing ${columnSpacing}px`);
    }

    const imageWidth = 500; // 图像宽度（用于缓冲区计算）

    // 计算网格左右边界
    const leftmostX = (-(cols - 1) / 2) * columnSpacing; // 最左侧图像X位置
    // PHOTOS5模式优化：使用真正的最右侧位置，而不是减去3列
    const rightmostX = this.isPhotos5Mode
      ? ((cols - 1) / 2) * columnSpacing // PHOTOS5: 真正的最右侧图像X位置
      : ((cols - 3) / 2) * columnSpacing; // 其他模式: 原有逻辑

    // 动态计算环绕边界 - 为无缝滚动优化
    const wrapLeftX = leftmostX - columnSpacing * 2; // 左边界：为无缝环绕提供足够空间
    // PHOTOS5模式优化：元素移出视口1个元素距离后就回到最右侧
    const wrapRightX = this.isPhotos5Mode
      ? rightmostX + columnSpacing // PHOTOS5: 1个元素距离就触发环绕
      : rightmostX + imageWidth + columnSpacing; // 其他模式：图像完全退出视图时触发环绕

    // 调试信息：输出PHOTOS5模式的环绕边界
    if (this.isPhotos5Mode) {
      console.log(`🎯 PHOTOS5 Wrap Boundaries: rightmostX=${rightmostX}, columnSpacing=${columnSpacing}, wrapRightX=${wrapRightX}`);
    }

    // 动态生成行设置：根据行数生成Y位置和滚动速度
    const rowSettings = this.generateRowSettings(rows);

    for (let i = 0; i < this.objects.length; i++) {
      const object = new THREE.Object3D();
      const row = Math.floor(i / cols); // 行优先排序
      const col = i % cols;
      const layer = Math.floor(i / (cols * rows));

      // 获取当前行设置
      const rowSetting = rowSettings[row % rows];

      // 设置网格位置
      object.position.x = (col - (cols - 1) / 2) * columnSpacing;
      object.position.y = rowSetting.y; // 使用行设置的Y位置
      object.position.z = layer * 800 - 400;

      // 创建网格滚动动画数据
      const gridScrollData = {
        row: row % rows, // 行号（0到rows-1）
        initialX: object.position.x, // 初始X位置（网格位置）
        currentX: object.position.x, // 当前X位置
        baseY: object.position.y, // 基础Y位置
        baseZ: object.position.z, // 基础Z位置
        speed: rowSetting.speed, // 滚动速度
        wrapLeftX: wrapLeftX, // 动态计算的左边界
        wrapRightX: wrapRightX, // 动态计算的右边界
      };

      this.gridScrollAnimationData.push(gridScrollData);
      this.targets.grid.push(object);

      // 使用正确的行信息更新数字覆盖层
      const actualObject = this.objects[i];
      if (actualObject && actualObject.userData && actualObject.userData.numberOverlay) {
        const actualRow = (row % rows) + 1; // 转换为基于1的行号
        actualObject.userData.numberOverlay.textContent = `图像 ${i + 1} - 第${actualRow}行`;
      }
    }
  }

  /**
   * 设置具有垂直滚动动画的翻转网格布局
   * @param {number} colCount - 网格列数（默认为3）
   */
  setupFlipGridLayout(colCount = 13) {
    // 清除现有的翻转网格布局和动画数据
    this.targets.flipGrid = [];
    this.flipGridScrollAnimationData = [];

    const cols = colCount;
    const rows = Math.ceil(this.objects.length / cols); // 计算指定列数所需的行数

    // 使用与网格布局相似的间距配置，但针对垂直布局进行调整
    let rowSpacing = 450; // 默认行间距
    let columnSpacing = 650; // 默认列间距

    // 根据模式调整间距
    if (this.isPhotos3Mode) {
      rowSpacing = 350; // PHOTOS3模式：更紧凑的行间距
      columnSpacing = 450;
      console.log(`🎯 FlipGrid PHOTOS3 Mode: Using compact spacing ${rowSpacing}px rows, ${columnSpacing}px cols`);
    } else if (this.isPhotos5Mode) {
      rowSpacing = 300; // PHOTOS5模式：更小的行间距
      columnSpacing = 700;
      console.log(`🎯 FlipGrid PHOTOS5 Mode: Using spacious spacing ${rowSpacing}px rows, ${columnSpacing}px cols`);
    }

    const imageHeight = 400; // 图像高度（用于缓冲区计算）

    // 计算网格上下边界
    const topmostY = ((rows - 1) / 2) * rowSpacing; // 最上方图像Y位置
    const bottommostY = (-(rows - 3) / 2) * rowSpacing; // 最下方图像Y位置

    // 动态计算环绕边界 - 为无缝垂直滚动优化
    const wrapTopY = topmostY + rowSpacing * 2; // 上边界：为无缝环绕提供足够空间
    const wrapBottomY = bottommostY - imageHeight - rowSpacing; // 下边界：图像完全退出视图时触发环绕

    // 动态生成列设置：根据列数生成X位置和滚动速度
    const columnSettings = this.generateColumnSettings(cols);

    for (let i = 0; i < this.objects.length; i++) {
      const object = new THREE.Object3D();
      const col = Math.floor(i / rows); // 列优先排序
      const row = i % rows;
      const layer = Math.floor(i / (rows * cols));

      // 获取当前列设置
      const columnSetting = columnSettings[col % cols];

      // 设置翻转网格位置
      object.position.x = columnSetting.x; // 使用列设置的X位置
      object.position.y = (row - (rows - 1) / 2) * rowSpacing;
      object.position.z = layer * 800 - 400;

      // 创建翻转网格滚动动画数据
      const flipGridScrollData = {
        col: col % cols, // 列号（0到cols-1）
        initialY: object.position.y, // 初始Y位置（网格位置）
        currentY: object.position.y, // 当前Y位置
        baseX: object.position.x, // 基础X位置
        baseZ: object.position.z, // 基础Z位置
        speed: columnSetting.speed, // 滚动速度
        wrapTopY: wrapTopY, // 动态计算的上边界
        wrapBottomY: wrapBottomY, // 动态计算的下边界
      };

      this.flipGridScrollAnimationData.push(flipGridScrollData);
      this.targets.flipGrid.push(object);

      // 使用正确的列信息更新数字覆盖层
      const actualObject = this.objects[i];
      if (actualObject && actualObject.userData && actualObject.userData.numberOverlay) {
        const actualCol = (col % cols) + 1; // 转换为基于1的列号
        actualObject.userData.numberOverlay.textContent = `图像 ${i + 1} - 第${actualCol}列`;
      }
    }
  }

  /**
   * 动态生成列设置：根据列数生成X位置和滚动速度
   * @param {number} cols - 列数
   * @returns {Array} 列设置数组，包含每列的X位置和滚动速度
   */
  generateColumnSettings(cols) {
    const columnSettings = [];

    // 配置不同列数布局的间距参数 - FLIP GRID使用更大的列间距
    const layoutConfigs = {
      3: {
        columnSpacing: 850, // 3列布局：列间距850px（增加200px）
        centerX: 0, // 中心X位置
        baseSpeed: 1.5, // 基础速度
        speedVariation: 0.5, // 速度变化范围
        speedPattern: [-3.5, -1.5, -4.0], // 增强速度差异：第一列快速，第二列中等速度，第三列最快
        directionPattern: [-1, -1, -1], // 所有列统一自上而下滚动
      },
      5: {
        columnSpacing: 600, // 5列布局：列间距600px（增加200px，更宽敞的水平分布）
        centerX: 0, // 中心X位置
        baseSpeed: 1.5, // 基础速度
        speedVariation: 0.3, // 速度变化范围（较小变化以保持一致性）
        speedPattern: [-3.5, -1.8, -2.8, -1.4, -4.2], // 增强速度差异：交替快慢模式，慢速列加快一些
        directionPattern: [-1, -1, -1, -1, -1], // 所有列统一自上而下滚动
      },
      generic: {
        columnSpacing: 750, // 通用布局：列间距750px（增加250px，适合多列布局）
        centerX: 0, // 中心X位置
        baseSpeed: 1.4, // 基础速度
        speedVariation: 0.4, // 速度变化范围
        speedPattern: null, // 使用动态计算
        directionPattern: null, // 使用动态计算
      },
    };

    // 选择配置：优先使用特定列数配置，否则使用通用配置
    const config = layoutConfigs[cols] || layoutConfigs.generic;

    for (let i = 0; i < cols; i++) {
      // 计算X位置：从中心点向左右均匀分布
      const x = config.centerX + (i - (cols - 1) / 2) * config.columnSpacing;

      let speed;
      let direction = 1; // 声明direction变量，默认值为1（向下）

      // PHOTOS3模式：所有列使用统一速度
      if (this.isPhotos3Mode) {
        speed = this.photos3UniformSpeed;
        // 为PHOTOS3模式设置统一自上而下方向（所有列都自上而下移动）
        direction = -1; // 所有列都自上而下移动
        if (i === 0) {
          console.log(`🎯 FlipGrid PHOTOS3 Uniform Speed: All ${cols} columns set to uniform speed ${speed} with top-to-bottom direction`);
        }
      } else if (this.isPhotos5Mode) {
        // PHOTOS5模式：所有列使用统一速度
        speed = this.photos5UniformSpeed;
        // 为PHOTOS5模式设置统一自上而下方向（所有列都自上而下移动）
        direction = -1; // 所有列都自上而下移动
        if (i === 0) {
          console.log(`🎯 FlipGrid PHOTOS5 Uniform Speed: All ${cols} columns set to uniform speed ${speed} with top-to-bottom direction`);
        }
      } else if (config.speedPattern && config.speedPattern.length === cols) {
        // 使用预定义的速度模式（如3列布局）
        speed = Math.abs(config.speedPattern[i]); // 取绝对值作为速度
        direction = config.speedPattern[i] >= 0 ? 1 : -1; // 根据正负号确定方向
      } else if (config.directionPattern && config.directionPattern.length === cols) {
        // 使用预定义的方向模式
        direction = config.directionPattern[i];
        // 动态计算速度：中间列较慢，边缘列较快
        const distanceFromCenter = Math.abs(i - (cols - 1) / 2);
        const maxDistance = (cols - 1) / 2;
        const normalizedDistance = maxDistance > 0 ? distanceFromCenter / maxDistance : 0;
        speed = config.baseSpeed + normalizedDistance * config.speedVariation;
      } else {
        // 动态计算速度和方向：所有列统一自上而下滚动，增强交替快慢效果
        direction = -1; // 所有列统一自上而下滚动

        // 创建交替快慢模式：偶数列快，奇数列慢
        const isEvenColumn = i % 2 === 0;
        const distanceFromCenter = Math.abs(i - (cols - 1) / 2);
        const maxDistance = (cols - 1) / 2;
        const normalizedDistance = maxDistance > 0 ? distanceFromCenter / maxDistance : 0;

        // 基础速度计算
        let baseCalculatedSpeed = config.baseSpeed + normalizedDistance * config.speedVariation;

        // 应用交替快慢模式：偶数列（0,2,4...）快速，奇数列（1,3,5...）慢速
        if (isEvenColumn) {
          speed = baseCalculatedSpeed * 1.8; // 偶数列快速（增加80%）
        } else {
          speed = baseCalculatedSpeed * 1.0; // 奇数列正常速度
        }

        // 确保最小速度差异
        speed = Math.max(speed, 0.5); // 最慢不低于0.5
        speed = Math.min(speed, 5.0); // 最快不超过5.0
      }

      // 应用方向到速度
      const finalSpeed = speed * direction;

      columnSettings.push({
        x: x,
        speed: finalSpeed,
      });
    }

    return columnSettings;
  }

  /**
   * 设置基于类别聚类的分组布局
   */
  setupGroupedLayout(photoData) {
    // 清除现有的分组布局
    this.targets.grouped = [];

    // 检查photoData是嵌套的（类别组数组）还是扁平的
    let categoryGroups;
    if (photoData.length > 0 && photoData[0].photos) {
      // 嵌套结构 - 直接使用
      categoryGroups = photoData;
    } else {
      // 扁平结构 - 按类别属性分组
      const categories = {};
      photoData.forEach((photo, index) => {
        const category = photo.category || "未分类";
        if (!categories[category]) {
          categories[category] = {
            category: category,
            title: category.charAt(0).toUpperCase() + category.slice(1) + " 集合",
            photos: [],
          };
        }
        categories[category].photos.push({ ...photo, originalIndex: index });
      });
      categoryGroups = Object.values(categories);
    }

    // 间距配置以获得更好的视觉分离
    const groupGap = 650; // 组边界之间的一致间隙（组之间的视觉间距）- 增加间距以获得更好的视觉分离
    // PHOTOS4 mode uses tighter column spacing for more compact layout
    const itemSpacing = this.currentLayout === "grouped4" ? 350 : 400; // PHOTOS4: 350px, PHOTOS2: 400px
    const rowHeight = 280; // 组内行之间的高度（从350减少到280，让行间距更紧凑）

    // 跟踪当前照片索引以进行扁平数组映射
    let currentPhotoIndex = 0;

    // 对齐修复：计算所有组中的最大行数以确保一致的顶部边缘对齐
    let maxRowsAcrossGroups = 0;
    const maxRows = 4; // 每个类别组的最大行数 - PHOTOS2模式限制为4行
    categoryGroups.forEach((categoryGroup) => {
      const totalPhotos = categoryGroup.photos.length;
      const actualRows = Math.min(totalPhotos, maxRows);
      maxRowsAcrossGroups = Math.max(maxRowsAcrossGroups, actualRows);
    });

    // 基于最大行数计算一致的顶部Y位置
    const globalTopY = ((maxRowsAcrossGroups - 1) * rowHeight) / 2;

    // 预计算组宽度和位置以获得一致的间距
    const groupData = [];
    let totalLayoutWidth = 0;

    categoryGroups.forEach((categoryGroup, groupIndex) => {
      const totalPhotos = categoryGroup.photos.length;
      const cols = Math.ceil(totalPhotos / maxRows);
      const actualRows = Math.min(totalPhotos, maxRows);

      // 基于列数计算此组的实际宽度
      const groupWidth = cols > 1 ? (cols - 1) * itemSpacing : 0; // 从最左侧到最右侧项目的宽度

      groupData.push({
        categoryGroup,
        groupIndex,
        cols,
        actualRows,
        totalPhotos,
        groupWidth,
        groupCenterX: 0, // 将在下面计算
      });

      // 添加到总布局宽度：组宽度 + 间隙（除了最后一组）
      totalLayoutWidth += groupWidth;
      if (groupIndex < categoryGroups.length - 1) {
        totalLayoutWidth += groupGap;
      }
    });

    // 计算起始X位置以居中整个布局
    let currentX = -totalLayoutWidth / 2;

    // 使用一致的边界间距定位每个组
    groupData.forEach((group) => {
      // 基于宽度和当前位置计算组中心X
      group.groupCenterX = currentX + group.groupWidth / 2;

      group.categoryGroup.photos.forEach((_, itemIndex) => {
        const object = new THREE.Object3D();

        // 计算网格中的位置：先填充列，然后填充行
        const col = Math.floor(itemIndex / maxRows);
        const row = itemIndex % maxRows;

        // 组内位置 - 改进的多行布局计算
        const itemX = group.groupCenterX + (col - (group.cols - 1) / 2) * itemSpacing;
        // 对齐修复：使用全局顶部Y位置确保所有组都有对齐的顶部边缘
        const itemY = globalTopY - row * rowHeight;
        // 对齐修复：对所有组使用相同的Z坐标以防止透视失真
        const itemZ = -400; // 所有组使用相同的Z位置以确保一致的视觉对齐

        object.position.set(itemX, itemY, itemZ);
        object.rotation.set(0, 0, 0);

        // 存储类别信息以便在覆盖层中可能使用
        object.userData = {
          category: group.categoryGroup.category,
          categoryTitle: group.categoryGroup.title,
          groupIndex: group.groupIndex,
          itemIndex: itemIndex,
        };

        // 在当前照片索引处存储到目标数组中
        this.targets.grouped[currentPhotoIndex] = object;
        currentPhotoIndex++;
      });

      // 移动到下一个组位置：当前位置 + 组宽度 + 间隙
      currentX += group.groupWidth + groupGap;
    });

    // 保存groupData以供飞入动画使用和category title创建
    this.lastGroupData = groupData;
    this.lastGlobalTopY = globalTopY;
    this.lastRowHeight = rowHeight;

    // 布局创建后，只有在没有飞入动画时才设置滚动动画
    if (!this.isGroupedFlyInActive) {
      this.setupGroupedScrollAnimation(groupData, totalLayoutWidth);
    }
  }

  /**
   * 更新分组布局 - 处理飞入动画和滚动动画
   */
  updateGroupedLayout() {
    // 如果处于聚焦模式，暂停所有动画
    if (this.isFocusMode) {
      return;
    }

    // 如果正在执行飞入动画，更新飞入动画而不是其他动画
    if (this.isGroupedFlyInActive) {
      this.updateGroupedFlyInAnimation();
      return;
    }

    // 如果PHOTOS4飞入动画正在进行，只更新飞入动画
    if (this.isGrouped4FlyInActive) {
      this.updateGrouped4FlyInAnimation();
      return;
    }

    // 飞入动画完成后，检查是否需要滚动动画
    if (this.groupedScrollAnimationData.length > 0) {
      this.updateGroupedScrollAnimation();
      // Also update category title headers for PHOTOS4 mode
      this.updateCategoryTitleLabels();
      // Also update category title2 headers for PHOTOS2 mode
      this.updateCategoryTitle2Labels();
      return;
    }

    // 如果没有滚动动画，保持静态位置
    for (let i = 0; i < this.objects.length; i++) {
      const object = this.objects[i];
      const target = this.targets.grouped[i];

      if (target) {
        // 确保对象保持在其分组位置（无浮动或漂移）
        object.position.copy(target.position);
        object.scale.set(1, 1, 1);
        object.rotation.set(0, 0, 0);
      }
    }

    // Update category title labels positions for PHOTOS4 mode
    this.updateCategoryTitleLabels();
    // Update category title2 labels positions for PHOTOS2 mode
    this.updateCategoryTitle2Labels();
  }

  /**
   * 设置网格飞入动画（适用于PHOTOS1、PHOTOS3和PHOTOS5模式）
   */
  setupGridFlyInAnimation() {
    // 清除现有的网格飞入动画数据
    this.gridFlyInAnimationData = [];

    // 为每个对象创建飞入动画数据
    for (let i = 0; i < this.objects.length; i++) {
      const object = this.objects[i];
      const target = this.targets.grid[i];

      if (!target) {
        continue;
      }

      // 计算飞入起始位置（从屏幕边缘）
      const flyInStartPos = this.calculateGridFlyInStartPosition(target.position, i);

      // 创建飞入动画数据
      const flyInData = {
        objectIndex: i,
        object: object,
        startPos: flyInStartPos,
        targetPos: { x: target.position.x, y: target.position.y, z: target.position.z },
        delay: Math.floor(i / this.gridFlyInBatchSize) * this.gridFlyInBatchDelay, // 分批延迟
        hasStarted: false,
        isComplete: false,
        animationStartTime: 0,
      };

      this.gridFlyInAnimationData.push(flyInData);

      // 将对象初始设置到飞入起始位置
      object.position.set(flyInStartPos.x, flyInStartPos.y, flyInStartPos.z);
      object.scale.set(1, 1, 1);
      object.rotation.set(0, 0, 0);

      // 添加飞入动画CSS类以禁用过渡
      if (object.element) {
        object.element.classList.add("fly-in-active");
        object.element.style.opacity = "0"; // 初始透明度为0
      }
    }
  }

  /**
   * 设置PHOTOS2模式的飞入动画
   */
  setupGroupedFlyInAnimation() {
    // 清除现有的飞入动画数据
    this.groupedFlyInAnimationData = [];

    // 为每个对象创建飞入动画数据
    for (let i = 0; i < this.objects.length; i++) {
      const object = this.objects[i];
      const target = this.targets.grouped[i];

      if (!target) {
        continue;
      }

      // 计算飞入起始位置（从屏幕边缘或随机方向）
      const flyInStartPos = this.calculateFlyInStartPosition(target.position, i);

      // 创建飞入动画数据
      const flyInData = {
        objectIndex: i,
        object: object,
        startPos: flyInStartPos,
        targetPos: { x: target.position.x, y: target.position.y, z: target.position.z },
        delay: Math.floor(i / this.groupedFlyInBatchSize) * this.groupedFlyInBatchDelay, // 分批延迟
        hasStarted: false,
        isComplete: false,
        animationStartTime: 0,
      };

      this.groupedFlyInAnimationData.push(flyInData);

      // 将对象初始设置到飞入起始位置
      object.position.set(flyInStartPos.x, flyInStartPos.y, flyInStartPos.z);
      object.scale.set(1, 1, 1);
      object.rotation.set(0, 0, 0);

      // 添加飞入动画CSS类以禁用过渡
      if (object.element) {
        object.element.classList.add("fly-in-active");
        object.element.style.opacity = "0"; // 初始透明度为0
      }
    }
  }

  /**
   * 设置PHOTOS4模式的飞入动画（使用与PHOTOS2完全一致的动画）
   */
  setupGrouped4FlyInAnimation() {
    // 清除现有的飞入动画数据
    this.grouped4FlyInAnimationData = [];

    // 为每个对象创建飞入动画数据
    for (let i = 0; i < this.objects.length; i++) {
      const object = this.objects[i];
      const target = this.targets.grouped[i];

      if (!target) {
        continue;
      }

      // 使用与PHOTOS2完全相同的飞入起始位置计算方法
      const flyInStartPos = this.calculateFlyInStartPosition(target.position, i);

      // 创建飞入动画数据（使用与PHOTOS2相同的参数）
      const flyInData = {
        objectIndex: i,
        object: object,
        startPos: flyInStartPos,
        targetPos: { x: target.position.x, y: target.position.y, z: target.position.z },
        delay: Math.floor(i / this.groupedFlyInBatchSize) * this.groupedFlyInBatchDelay, // 使用PHOTOS2的批次参数
        hasStarted: false,
        isComplete: false,
        animationStartTime: 0,
      };

      this.grouped4FlyInAnimationData.push(flyInData);

      // 将对象初始设置到飞入起始位置
      object.position.set(flyInStartPos.x, flyInStartPos.y, flyInStartPos.z);
      object.scale.set(1, 1, 1);
      object.rotation.set(0, 0, 0);

      // 添加飞入动画CSS类以禁用过渡
      if (object.element) {
        object.element.classList.add("fly-in-active");
        object.element.style.opacity = "0"; // 初始透明度为0
      }
    }
  }

  /**
   * 计算网格飞入动画的起始位置
   */
  calculateGridFlyInStartPosition(targetPos, index) {
    // 定义网格飞入方向（适合网格布局的方向）
    const flyInTypes = [
      "left", // 从左侧飞入
      "right", // 从右侧飞入
      "top", // 从上方飞入
      "bottom", // 从下方飞入
    ];

    // 根据索引选择飞入类型，确保有一定的随机性但保持协调
    const typeIndex = index % flyInTypes.length;
    const flyInType = flyInTypes[typeIndex];

    // 计算视口边界
    const camera = this.camera;
    const distance = Math.abs(camera.position.z);
    const vFOV = (camera.fov * Math.PI) / 180;
    const height = 2 * Math.tan(vFOV / 2) * distance;
    const width = height * camera.aspect;

    const margin = 800; // 屏幕外边距（适合网格布局）

    let startPos = { x: targetPos.x, y: targetPos.y, z: targetPos.z };

    switch (flyInType) {
      case "left":
        startPos.x = -width / 2 - margin;
        break;
      case "right":
        startPos.x = width / 2 + margin;
        break;
      case "top":
        startPos.y = height / 2 + margin;
        break;
      case "bottom":
        startPos.y = -height / 2 - margin;
        break;
    }

    return startPos;
  }

  /**
   * 计算分组飞入动画的起始位置
   */
  calculateFlyInStartPosition(targetPos, index) {
    // 定义多种飞入方向
    const flyInTypes = [
      "left", // 从左侧飞入
      "right", // 从右侧飞入
      "top", // 从上方飞入
      "bottom", // 从下方飞入
      "center", // 从中心扩散飞入
    ];

    // 根据索引选择飞入类型，确保有一定的随机性但保持协调
    const typeIndex = index % flyInTypes.length;
    const flyInType = flyInTypes[typeIndex];

    // 计算视口边界
    const camera = this.camera;
    const distance = Math.abs(camera.position.z);
    const vFOV = (camera.fov * Math.PI) / 180;
    const height = 2 * Math.tan(vFOV / 2) * distance;
    const width = height * camera.aspect;

    const margin = 1000; // 屏幕外边距（增加距离使动画更明显）

    let startPos = { x: targetPos.x, y: targetPos.y, z: targetPos.z };

    switch (flyInType) {
      case "left":
        startPos.x = -width / 2 - margin;
        break;
      case "right":
        startPos.x = width / 2 + margin;
        break;
      case "top":
        startPos.y = height / 2 + margin;
        break;
      case "bottom":
        startPos.y = -height / 2 - margin;
        break;
      case "center":
        // 从中心点开始，稍微偏移
        startPos.x = 0;
        startPos.y = 0;
        startPos.z = targetPos.z - 1000; // 从更远的Z位置飞入
        break;
    }

    return startPos;
  }

  /**
   * 更新网格飞入动画（适用于PHOTOS1、PHOTOS3和PHOTOS5模式）
   */
  updateGridFlyInAnimation() {
    if (!this.isGridFlyInActive || this.gridFlyInAnimationData.length === 0) {
      return;
    }

    const currentTime = Date.now();
    const globalElapsed = currentTime - this.gridFlyInStartTime;
    let allComplete = true;

    for (const flyInData of this.gridFlyInAnimationData) {
      // 检查是否到了这个对象开始动画的时间
      if (!flyInData.hasStarted && globalElapsed >= flyInData.delay) {
        flyInData.hasStarted = true;
        flyInData.animationStartTime = currentTime;
      }

      // 如果动画已开始但未完成，更新位置
      if (flyInData.hasStarted && !flyInData.isComplete) {
        const animationElapsed = currentTime - flyInData.animationStartTime;
        const progress = Math.min(animationElapsed / this.gridFlyInDuration, 1);

        // 使用缓动函数
        const easeProgress = this.easeInOutCubic(progress);

        // 插值计算当前位置
        const currentPos = {
          x: flyInData.startPos.x + (flyInData.targetPos.x - flyInData.startPos.x) * easeProgress,
          y: flyInData.startPos.y + (flyInData.targetPos.y - flyInData.startPos.y) * easeProgress,
          z: flyInData.startPos.z + (flyInData.targetPos.z - flyInData.startPos.z) * easeProgress,
        };

        // 应用位置
        flyInData.object.position.set(currentPos.x, currentPos.y, currentPos.z);
        flyInData.object.scale.set(1, 1, 1);
        flyInData.object.rotation.set(0, 0, 0);

        // 在动画期间添加透明度效果
        if (flyInData.object.element) {
          flyInData.object.element.style.opacity = easeProgress.toString();
        }

        // 检查动画是否完成
        if (progress >= 1) {
          flyInData.isComplete = true;
          flyInData.object.position.set(flyInData.targetPos.x, flyInData.targetPos.y, flyInData.targetPos.z);

          // 移除飞入动画CSS类以恢复正常过渡
          if (flyInData.object.element) {
            flyInData.object.element.classList.remove("fly-in-active");
            flyInData.object.element.style.opacity = "1"; // 确保透明度恢复到1
          }
        }
      }

      // 检查是否还有未完成的动画
      if (!flyInData.isComplete) {
        allComplete = false;
      }
    }

    // 如果所有动画都完成了，结束飞入动画状态
    if (allComplete) {
      this.isGridFlyInActive = false;
      this.gridFlyInAnimationData = [];

      // 飞入动画完成后，设置正常的网格滚动动画

      // 立即检查视口状态以启用滚动动画
      this.checkViewportAndUpdateScrolling();
    }
  }

  /**
   * 更新PHOTOS2飞入动画
   */
  updateGroupedFlyInAnimation() {
    if (!this.isGroupedFlyInActive || this.groupedFlyInAnimationData.length === 0) {
      return;
    }

    const currentTime = Date.now();
    const globalElapsed = currentTime - this.groupedFlyInStartTime;
    let allComplete = true;

    for (const flyInData of this.groupedFlyInAnimationData) {
      // 检查是否到了这个对象开始动画的时间
      if (!flyInData.hasStarted && globalElapsed >= flyInData.delay) {
        flyInData.hasStarted = true;
        flyInData.animationStartTime = currentTime;
      }

      // 如果动画已开始但未完成，更新位置
      if (flyInData.hasStarted && !flyInData.isComplete) {
        const animationElapsed = currentTime - flyInData.animationStartTime;
        const progress = Math.min(animationElapsed / this.groupedFlyInDuration, 1);

        // 使用缓动函数
        const easeProgress = this.easeInOutCubic(progress);

        // 插值计算当前位置
        const currentPos = {
          x: flyInData.startPos.x + (flyInData.targetPos.x - flyInData.startPos.x) * easeProgress,
          y: flyInData.startPos.y + (flyInData.targetPos.y - flyInData.startPos.y) * easeProgress,
          z: flyInData.startPos.z + (flyInData.targetPos.z - flyInData.startPos.z) * easeProgress,
        };

        // 应用位置
        flyInData.object.position.set(currentPos.x, currentPos.y, currentPos.z);
        flyInData.object.scale.set(1, 1, 1);
        flyInData.object.rotation.set(0, 0, 0);

        // 在动画期间添加透明度效果
        if (flyInData.object.element) {
          flyInData.object.element.style.opacity = easeProgress.toString();
        }

        // 检查动画是否完成
        if (progress >= 1) {
          flyInData.isComplete = true;
          flyInData.object.position.set(flyInData.targetPos.x, flyInData.targetPos.y, flyInData.targetPos.z);

          // 移除飞入动画CSS类以恢复正常过渡
          if (flyInData.object.element) {
            flyInData.object.element.classList.remove("fly-in-active");
            flyInData.object.element.style.opacity = "1"; // 确保透明度恢复到1
          }
        }
      }

      // 检查是否还有未完成的动画
      if (!flyInData.isComplete) {
        allComplete = false;
      }
    }

    // 如果所有动画都完成了，结束飞入动画状态
    if (allComplete) {
      this.isGroupedFlyInActive = false;
      this.groupedFlyInAnimationData = [];

      // 飞入动画完成后，设置正常的滚动动画（如果数据可用）
      if (this.lastGroupData && this.groupedLayoutBounds.totalWidth > 0) {
        this.setupGroupedScrollAnimation(this.lastGroupData, this.groupedLayoutBounds.totalWidth);
      }
    }
  }

  /**
   * 更新PHOTOS4飞入动画（使用与PHOTOS2完全一致的动画）
   */
  updateGrouped4FlyInAnimation() {
    if (!this.isGrouped4FlyInActive || this.grouped4FlyInAnimationData.length === 0) {
      return;
    }

    const currentTime = Date.now();
    const globalElapsed = currentTime - this.grouped4FlyInStartTime;
    let allComplete = true;

    for (const flyInData of this.grouped4FlyInAnimationData) {
      // 检查是否到了这个对象开始动画的时间
      if (!flyInData.hasStarted && globalElapsed >= flyInData.delay) {
        flyInData.hasStarted = true;
        flyInData.animationStartTime = currentTime;
      }

      // 如果动画已开始但未完成，更新位置
      if (flyInData.hasStarted && !flyInData.isComplete) {
        const animationElapsed = currentTime - flyInData.animationStartTime;
        const progress = Math.min(animationElapsed / this.groupedFlyInDuration, 1); // 使用PHOTOS2的持续时间

        // 使用与PHOTOS2相同的缓动函数
        const easeProgress = this.easeInOutCubic(progress);

        // 插值计算当前位置
        const currentPos = {
          x: flyInData.startPos.x + (flyInData.targetPos.x - flyInData.startPos.x) * easeProgress,
          y: flyInData.startPos.y + (flyInData.targetPos.y - flyInData.startPos.y) * easeProgress,
          z: flyInData.startPos.z + (flyInData.targetPos.z - flyInData.startPos.z) * easeProgress,
        };

        // 应用位置
        flyInData.object.position.set(currentPos.x, currentPos.y, currentPos.z);
        flyInData.object.scale.set(1, 1, 1);
        flyInData.object.rotation.set(0, 0, 0);

        // 使用与PHOTOS2相同的透明度效果
        if (flyInData.object.element) {
          flyInData.object.element.style.opacity = easeProgress.toString();
        }

        // 检查动画是否完成
        if (progress >= 1) {
          flyInData.isComplete = true;
          flyInData.object.position.set(flyInData.targetPos.x, flyInData.targetPos.y, flyInData.targetPos.z);

          // 移除飞入动画CSS类以恢复正常过渡
          if (flyInData.object.element) {
            flyInData.object.element.classList.remove("fly-in-active");
            flyInData.object.element.style.opacity = "1"; // 确保透明度恢复到1
          }
        }
      }

      // 检查是否还有未完成的动画
      if (!flyInData.isComplete) {
        allComplete = false;
      }
    }

    // 如果所有动画都完成了，结束飞入动画状态
    if (allComplete) {
      this.isGrouped4FlyInActive = false;
      this.grouped4FlyInAnimationData = [];

      // 飞入动画完成后，设置正常的滚动动画（如果数据可用）
      if (this.lastGroupData && this.groupedLayoutBounds.totalWidth > 0) {
        this.setupGroupedScrollAnimation(this.lastGroupData, this.groupedLayoutBounds.totalWidth);
      }
    }
  }

  /**
   * 设置分组布局基于列的水平滚动动画
   */
  setupGroupedScrollAnimation(groupData, totalLayoutWidth) {
    // 清除现有的分组滚动动画数据
    this.groupedScrollAnimationData = [];

    // 计算视口宽度以检测溢出
    const camera = this.camera;
    const distance = Math.abs(camera.position.z);
    const vFOV = (camera.fov * Math.PI) / 180;
    const height = 2 * Math.tan(vFOV / 2) * distance;
    const viewportWidth = height * camera.aspect;

    // 存储布局尺寸
    this.groupedLayoutBounds = {
      totalWidth: totalLayoutWidth,
      viewportWidth: viewportWidth,
    };

    // 检查内容是否溢出视口并需要滚动
    const needsScrolling = totalLayoutWidth > viewportWidth;
    this.isGroupedScrollingEnabled = needsScrolling;

    // Setup category title header scroll animation for PHOTOS4 mode
    this.setupCategoryTitleScrollAnimation(needsScrolling);

    // Setup category title2 header scroll animation for PHOTOS2 mode
    this.setupCategoryTitle2ScrollAnimation(needsScrolling);

    if (!needsScrolling) {
      return;
    }

    // 获取原始布局的间距配置
    // PHOTOS4 mode uses tighter column spacing for more compact layout
    const itemSpacing = this.currentLayout === "grouped4" ? 350 : 400; // PHOTOS4: 350px, PHOTOS2: 400px
    console.log("🎯 PHOTOS4 Scroll Spacing ~ itemSpacing:", itemSpacing, "for layout:", this.currentLayout);
    const groupGap = 650; // 组间间距，与setupGroupedLayout中相同

    // 计算考虑组间间距的环绕边界
    // 使用更大的缓冲区来确保环绕时保持原始间距
    const wrapBuffer = Math.max(itemSpacing, groupGap); // 使用较大的间距作为缓冲区
    const wrapLeftX = -totalLayoutWidth / 2 - wrapBuffer;
    const wrapRightX = totalLayoutWidth / 2.2 + wrapBuffer;

    // 重新组织照片：按列而不是按组，但保留组信息以维持间距
    // 首先收集所有照片的位置信息，按X坐标分组为列
    const allPhotos = [];
    let photoIndex = 0;

    groupData.forEach((group) => {
      group.categoryGroup.photos.forEach((_, itemIndex) => {
        const target = this.targets.grouped[photoIndex];
        if (target) {
          allPhotos.push({
            photoIndex: photoIndex,
            x: target.position.x,
            y: target.position.y,
            z: target.position.z,
            groupIndex: group.groupIndex,
            itemIndex: itemIndex,
            groupCenterX: group.groupCenterX, // 保留组中心信息
            groupWidth: group.groupWidth, // 保留组宽度信息
          });
        }
        photoIndex++;
      });
    });

    // 按X坐标对所有照片进行排序，然后按列分组
    allPhotos.sort((a, b) => a.x - b.x);

    // 将照片按列分组（基于X坐标的相似性）
    const columns = [];
    const tolerance = itemSpacing * 0.1; // 10%的容差来判断是否属于同一列

    allPhotos.forEach((photo) => {
      // 查找是否有现有列可以容纳这张照片
      let foundColumn = null;
      for (const column of columns) {
        if (Math.abs(column.x - photo.x) <= tolerance) {
          column.photos.push(photo);
          foundColumn = column;
          break;
        }
      }

      // 如果没有找到合适的列，创建新列
      if (!foundColumn) {
        columns.push({
          x: photo.x,
          photos: [photo],
          // 存储此列涉及的组信息，用于计算间距
          groupsInvolved: [photo.groupIndex],
        });
      } else {
        // 更新找到的列涉及的组信息
        if (!foundColumn.groupsInvolved.includes(photo.groupIndex)) {
          foundColumn.groupsInvolved.push(photo.groupIndex);
        }
      }
    });

    // 为每列创建滚动动画数据，考虑组间间距
    columns.forEach((column, columnIndex) => {
      // 所有列使用统一的滚动速度
      const columnSpeed = this.groupedColumnScrollSpeed;

      // 计算此列的间距特征
      const isMultiGroupColumn = column.groupsInvolved.length > 1;

      const columnScrollData = {
        columnIndex: columnIndex,
        originalX: column.x,
        currentX: column.x, // 滚动的当前X位置
        speed: columnSpeed, // 统一的列滚动速度
        wrapLeftX: wrapLeftX,
        wrapRightX: wrapRightX,
        photoIndices: column.photos.map((photo) => photo.photoIndex), // 此列中照片的索引
        photoCount: column.photos.length,
        groupsInvolved: column.groupsInvolved, // 此列涉及的组
        isMultiGroupColumn: isMultiGroupColumn, // 是否跨越多个组
        // 存储间距配置用于环绕计算
        itemSpacing: itemSpacing,
        groupGap: groupGap,
      };

      this.groupedScrollAnimationData.push(columnScrollData);
    });
  }

  /**
   * Setup category title header scroll animation for PHOTOS4 mode
   * @param {boolean} needsScrolling - Whether scrolling is needed based on content overflow
   */
  setupCategoryTitleScrollAnimation(needsScrolling) {
    // Clear existing category title scroll animation data
    this.categoryTitleScrollAnimationData = [];
    this.isCategoryTitleScrollingEnabled = needsScrolling;

    if (!needsScrolling || this.categoryTitleObjects.length === 0) {
      return;
    }

    // Create scroll animation data for each category title header
    this.categoryTitleObjects.forEach((titleObject, index) => {
      const target = this.categoryTitleTargets[index];
      if (!target) {
        return;
      }

      // Calculate wrap boundaries for seamless scrolling (same as grouped images)
      const camera = this.camera;
      const distance = Math.abs(camera.position.z);
      const vFOV = (camera.fov * Math.PI) / 180;
      const height = 2 * Math.tan(vFOV / 2) * distance;
      const viewportWidth = height * camera.aspect;

      const wrapRightX = viewportWidth * 0.6; // Right boundary for wrapping
      const wrapLeftX = -viewportWidth * 0.6; // Left boundary for wrapping

      const titleScrollData = {
        titleIndex: index,
        titleObject: titleObject,
        originalX: target.position.x,
        currentX: target.position.x, // Current X position for scrolling
        speed: this.groupedColumnScrollSpeed, // Use same speed as grouped images
        wrapLeftX: wrapLeftX,
        wrapRightX: wrapRightX,
        target: target,
      };

      this.categoryTitleScrollAnimationData.push(titleScrollData);
    });

    console.log(`🎯 PHOTOS4 Header Animation: Setup scroll animation for ${this.categoryTitleScrollAnimationData.length} category title headers`);
  }

  /**
   * Check viewport and update category title scrolling state
   */
  checkCategoryTitleViewportAndUpdateScrolling() {
    if (this.categoryTitleScrollAnimationData.length === 0) {
      return;
    }

    // Use the same viewport bounds as grouped layout
    const needsScrolling = this.groupedLayoutBounds.totalWidth > this.groupedLayoutBounds.viewportWidth;

    if (needsScrolling !== this.isCategoryTitleScrollingEnabled) {
      this.isCategoryTitleScrollingEnabled = needsScrolling;
      console.log(`🎯 PHOTOS4 Header Animation: Category title scrolling ${needsScrolling ? "enabled" : "disabled"} based on viewport`);
    }
  }

  /**
   * Update category title header scroll animation with real-time dynamic positioning
   * Headers track their corresponding content columns for perfect synchronization
   */
  updateCategoryTitleScrollAnimation() {
    if (this.categoryTitleScrollAnimationData.length === 0) {
      return;
    }

    // If in focus mode or animations are paused, stop scroll animation immediately
    if (this.isFocusMode || this.animationsPaused) {
      // Keep headers in their current static positions when animations are paused
      for (const titleScrollData of this.categoryTitleScrollAnimationData) {
        titleScrollData.titleObject.position.x = titleScrollData.target.position.x;
        titleScrollData.titleObject.position.y = titleScrollData.target.position.y;
        titleScrollData.titleObject.position.z = titleScrollData.target.position.z;
        titleScrollData.titleObject.scale.set(1, 1, 1);
        titleScrollData.titleObject.rotation.set(0, 0, 0);
      }
      return;
    }

    // Periodically check viewport state (same interval as grouped layout)
    const currentTime = Date.now();
    if (currentTime - this.lastViewportCheck > this.viewportCheckInterval) {
      this.checkCategoryTitleViewportAndUpdateScrolling();
    }

    if (!this.isCategoryTitleScrollingEnabled) {
      // If scrolling is disabled, keep headers in static positions
      for (const titleScrollData of this.categoryTitleScrollAnimationData) {
        titleScrollData.titleObject.position.x = titleScrollData.target.position.x;
        titleScrollData.titleObject.position.y = titleScrollData.target.position.y;
        titleScrollData.titleObject.position.z = titleScrollData.target.position.z;
        titleScrollData.titleObject.scale.set(1, 1, 1);
        titleScrollData.titleObject.rotation.set(0, 0, 0);
      }
      return;
    }

    // REAL-TIME DYNAMIC POSITIONING: Calculate header positions based on actual content column positions
    for (const titleScrollData of this.categoryTitleScrollAnimationData) {
      const titleObject = titleScrollData.titleObject;
      const groupIndex = titleObject.userData?.groupIndex;

      if (groupIndex !== undefined) {
        // Find all content columns that belong to this group
        const groupColumns = this.groupedScrollAnimationData.filter((columnData) => columnData.groupsInvolved.includes(groupIndex));

        if (groupColumns.length > 0) {
          // Collect valid column positions
          const validColumns = [];
          for (const columnData of groupColumns) {
            // Safety check: ensure columnData and currentX are valid
            if (columnData && typeof columnData.currentX === "number" && !isNaN(columnData.currentX)) {
              validColumns.push({
                x: columnData.currentX,
                columnData: columnData,
              });
            }
          }

          // Safety check: ensure we have valid columns to calculate center
          if (validColumns.length > 0) {
            // Calculate viewport bounds for split detection
            const camera = this.camera;
            const distance = Math.abs(camera.position.z);
            const vFOV = (camera.fov * Math.PI) / 180;
            const height = 2 * Math.tan(vFOV / 2) * distance;
            const viewportWidth = height * camera.aspect;
            const viewportLeft = -viewportWidth / 2;
            const viewportRight = viewportWidth / 2;

            // Detect if columns are split across viewport (some on left, some on right)
            const splitThreshold = viewportWidth * 0.25; // 25% of viewport width as threshold
            const leftSideColumns = validColumns.filter((col) => col.x < viewportLeft + splitThreshold);
            const rightSideColumns = validColumns.filter((col) => col.x > viewportRight - splitThreshold);
            const centerColumns = validColumns.filter((col) => col.x >= viewportLeft + splitThreshold && col.x <= viewportRight - splitThreshold);

            let groupCenterX;
            let positioningMode = "center-all";

            // Check if columns are split across viewport (columns on both left and right sides with gap in center)
            if (leftSideColumns.length > 0 && rightSideColumns.length > 0 && centerColumns.length === 0) {
              // Columns are split - position header above rightmost cluster
              positioningMode = "rightmost-cluster";

              // Find the rightmost cluster of columns
              const rightmostX = Math.max(...rightSideColumns.map((col) => col.x));
              const clusterThreshold = viewportWidth * 0.2; // Columns within 20% of viewport width are considered a cluster
              const rightCluster = rightSideColumns.filter((col) => Math.abs(col.x - rightmostX) < clusterThreshold);

              if (rightCluster.length > 0) {
                // Calculate center of rightmost cluster
                groupCenterX = rightCluster.reduce((sum, col) => sum + col.x, 0) / rightCluster.length;
              } else {
                // Fallback to rightmost column
                groupCenterX = rightmostX;
              }
            } else if (rightSideColumns.length > leftSideColumns.length && rightSideColumns.length > centerColumns.length) {
              // Prefer rightmost columns when they are the majority
              positioningMode = "prefer-right";
              const visibleRightColumns = rightSideColumns.filter((col) => col.x <= viewportRight + viewportWidth * 0.1);
              if (visibleRightColumns.length > 0) {
                groupCenterX = visibleRightColumns.reduce((sum, col) => sum + col.x, 0) / visibleRightColumns.length;
              } else {
                groupCenterX = validColumns.reduce((sum, col) => sum + col.x, 0) / validColumns.length;
              }
            } else {
              // Normal case: calculate center of all visible columns
              groupCenterX = validColumns.reduce((sum, col) => sum + col.x, 0) / validColumns.length;
            }

            // Apply the calculated position with instant positioning for seamless wrapping
            const element = titleObject.element;

            // Check if any column in this group just wrapped (for seamless header wrapping)
            // A column has wrapped if it was near the right boundary and is now near the left boundary
            const anyColumnWrapped = groupColumns.some((columnData) => {
              const previousX = columnData.currentX - columnData.speed;
              const hasWrapped = previousX > columnData.wrapRightX - columnData.speed * 2 && columnData.currentX < columnData.wrapLeftX + columnData.speed * 2;
              return hasWrapped;
            });

            if (anyColumnWrapped) {
              // Temporarily disable CSS transitions for instant positioning during wrapping
              if (element) {
                element.classList.add("instant-position");
              }

              // Apply instant position update
              titleObject.position.x = groupCenterX;

              // Re-enable transitions after a brief delay
              setTimeout(() => {
                if (element) {
                  element.classList.remove("instant-position");
                }
              }, 0);
            } else {
              // Normal smooth positioning - directly track the group center
              titleObject.position.x = groupCenterX;
            }

            // Performance optimization: Update tracking data for debugging (throttled)
            if (titleScrollData.lastGroupCenterX !== undefined) {
              const deltaX = Math.abs(groupCenterX - titleScrollData.lastGroupCenterX);
              // Only log significant movements and throttle logging to avoid performance impact
              if (deltaX > 5.0 && (!titleScrollData.lastLogTime || currentTime - titleScrollData.lastLogTime > 1000)) {
                console.log(
                  `🎯 PHOTOS4 Smart Header: Group ${groupIndex} positioned at ${groupCenterX.toFixed(1)} (mode: ${positioningMode}, columns: L${leftSideColumns.length}/C${
                    centerColumns.length
                  }/R${rightSideColumns.length})`
                );
                titleScrollData.lastLogTime = currentTime;
              }
            }
            titleScrollData.lastGroupCenterX = groupCenterX;
          } else {
            // Fallback: No valid columns found for this group, use static position
            titleObject.position.x = titleScrollData.target.position.x;
          }
        } else {
          // Fallback: No columns found for this group, use static position
          titleObject.position.x = titleScrollData.target.position.x;
        }
      } else {
        // Fallback: No group index, use static position
        titleObject.position.x = titleScrollData.target.position.x;
      }

      // Maintain Y and Z positions, scale, and rotation
      titleObject.position.y = titleScrollData.target.position.y;
      titleObject.position.z = titleScrollData.target.position.z;
      titleObject.scale.set(1, 1, 1);
      titleObject.rotation.set(0, 0, 0);
    }
  }

  /**
   * Setup category title2 header scroll animation for PHOTOS2 mode
   * @param {boolean} needsScrolling - Whether scrolling is needed based on content overflow
   */
  setupCategoryTitle2ScrollAnimation(needsScrolling) {
    // Clear existing category title2 scroll animation data
    this.categoryTitle2ScrollAnimationData = [];
    this.isCategoryTitle2ScrollingEnabled = needsScrolling;

    if (!needsScrolling || this.categoryTitle2Objects.length === 0) {
      return;
    }

    // Create scroll animation data for each category title2 header
    this.categoryTitle2Objects.forEach((titleObject, index) => {
      const target = this.categoryTitle2Targets[index];
      if (!target) {
        return;
      }

      // Calculate scroll animation parameters for title2 headers
      const scrollSpeed = this.groupedColumnScrollSpeed; // Use same speed as grouped photos for synchronization
      const totalLayoutWidth = this.groupedLayoutBounds.totalWidth;
      const viewportWidth = this.groupedLayoutBounds.viewportWidth;
      const seamlessWrapDistance = totalLayoutWidth + viewportWidth;

      this.categoryTitle2ScrollAnimationData.push({
        titleObject: titleObject,
        target: target,
        speed: scrollSpeed,
        currentX: target.position.x,
        originalX: target.position.x,
        seamlessWrapDistance: seamlessWrapDistance,
        // Add tracking data for smart positioning
        lastGroupCenterX: undefined,
        lastLogTime: undefined,
      });
    });
  }

  /**
   * Update category title2 header scroll animation with smart positioning (PHOTOS2 mode)
   */
  updateCategoryTitle2ScrollAnimation() {
    if (this.categoryTitle2ScrollAnimationData.length === 0) {
      return;
    }

    // Periodically check viewport state (same interval as grouped layout)
    const currentTime = Date.now();
    if (currentTime - this.lastViewportCheck > this.viewportCheckInterval) {
      this.checkCategoryTitle2ViewportAndUpdateScrolling();
    }

    if (!this.isCategoryTitle2ScrollingEnabled) {
      // If scrolling is disabled, keep headers in static positions
      for (const title2ScrollData of this.categoryTitle2ScrollAnimationData) {
        title2ScrollData.titleObject.position.x = title2ScrollData.target.position.x;
        title2ScrollData.titleObject.position.y = title2ScrollData.target.position.y;
        title2ScrollData.titleObject.position.z = title2ScrollData.target.position.z;
        title2ScrollData.titleObject.scale.set(1, 1, 1);
        title2ScrollData.titleObject.rotation.set(0, 0, 0);
      }
      return;
    }

    // REAL-TIME DYNAMIC POSITIONING: Calculate header positions based on actual content column positions
    for (const title2ScrollData of this.categoryTitle2ScrollAnimationData) {
      const titleObject = title2ScrollData.titleObject;
      const groupIndex = titleObject.userData?.groupIndex;

      if (groupIndex !== undefined) {
        // Find all content columns that belong to this group
        const groupColumns = this.groupedScrollAnimationData.filter((columnData) => columnData.groupsInvolved.includes(groupIndex));

        if (groupColumns.length > 0) {
          // Collect valid column positions
          const validColumns = [];
          for (const columnData of groupColumns) {
            // Safety check: ensure columnData and currentX are valid
            if (columnData && typeof columnData.currentX === "number" && !isNaN(columnData.currentX)) {
              validColumns.push({
                x: columnData.currentX,
                columnData: columnData,
              });
            }
          }

          // Safety check: ensure we have valid columns to calculate center
          if (validColumns.length > 0) {
            // Calculate viewport bounds for split detection
            const camera = this.camera;
            const distance = Math.abs(camera.position.z);
            const vFOV = (camera.fov * Math.PI) / 180;
            const height = 2 * Math.tan(vFOV / 2) * distance;
            const viewportWidth = height * camera.aspect;
            const viewportLeft = -viewportWidth / 2;
            const viewportRight = viewportWidth / 2;

            // Detect if columns are split across viewport (some on left, some on right)
            const splitThreshold = viewportWidth * 0.25; // 25% of viewport width as threshold
            const leftSideColumns = validColumns.filter((col) => col.x < viewportLeft + splitThreshold);
            const rightSideColumns = validColumns.filter((col) => col.x > viewportRight - splitThreshold);
            const centerColumns = validColumns.filter((col) => col.x >= viewportLeft + splitThreshold && col.x <= viewportRight - splitThreshold);

            let groupCenterX;
            let positioningMode = "center-all";

            // Check if columns are split across viewport (columns on both left and right sides with gap in center)
            if (leftSideColumns.length > 0 && rightSideColumns.length > 0 && centerColumns.length === 0) {
              // Columns are split - position header above rightmost cluster
              positioningMode = "rightmost-cluster";

              // Find the rightmost cluster of columns
              const rightmostX = Math.max(...rightSideColumns.map((col) => col.x));
              const clusterThreshold = viewportWidth * 0.2; // Columns within 20% of viewport width are considered a cluster
              const rightCluster = rightSideColumns.filter((col) => Math.abs(col.x - rightmostX) < clusterThreshold);

              if (rightCluster.length > 0) {
                // Calculate center of rightmost cluster
                groupCenterX = rightCluster.reduce((sum, col) => sum + col.x, 0) / rightCluster.length;
              } else {
                // Fallback to rightmost column
                groupCenterX = rightmostX;
              }
            } else if (rightSideColumns.length > leftSideColumns.length && rightSideColumns.length > centerColumns.length) {
              // Prefer rightmost columns when they are the majority
              positioningMode = "prefer-right";
              const visibleRightColumns = rightSideColumns.filter((col) => col.x <= viewportRight + viewportWidth * 0.1);
              if (visibleRightColumns.length > 0) {
                groupCenterX = visibleRightColumns.reduce((sum, col) => sum + col.x, 0) / visibleRightColumns.length;
              } else {
                groupCenterX = validColumns.reduce((sum, col) => sum + col.x, 0) / validColumns.length;
              }
            } else {
              // Normal case: calculate center of all visible columns
              groupCenterX = validColumns.reduce((sum, col) => sum + col.x, 0) / validColumns.length;
            }

            // Apply the calculated position with instant positioning for seamless wrapping
            const element = titleObject.element;

            // Check if any column in this group just wrapped (for seamless header wrapping)
            // A column has wrapped if it was near the right boundary and is now near the left boundary
            const anyColumnWrapped = groupColumns.some((columnData) => {
              const previousX = columnData.currentX - columnData.speed;
              const hasWrapped = previousX > columnData.wrapRightX - columnData.speed * 2 && columnData.currentX < columnData.wrapLeftX + columnData.speed * 2;
              return hasWrapped;
            });

            if (anyColumnWrapped) {
              // Temporarily disable CSS transitions for instant positioning during wrapping
              if (element) {
                element.classList.add("instant-position");
              }

              // Apply instant position update
              titleObject.position.x = groupCenterX;

              // Re-enable transitions after a brief delay
              setTimeout(() => {
                if (element) {
                  element.classList.remove("instant-position");
                }
              }, 0);
            } else {
              // Normal smooth positioning - directly track the group center
              titleObject.position.x = groupCenterX;
            }

            // Performance optimization: Update tracking data for debugging (throttled)
            if (title2ScrollData.lastGroupCenterX !== undefined) {
              const deltaX = Math.abs(groupCenterX - title2ScrollData.lastGroupCenterX);
              // Only log significant movements and throttle logging to avoid performance impact
              if (deltaX > 5.0 && (!title2ScrollData.lastLogTime || currentTime - title2ScrollData.lastLogTime > 1000)) {
                console.log(
                  `🎯 PHOTOS2 Smart Header: Group ${groupIndex} positioned at ${groupCenterX.toFixed(1)} (mode: ${positioningMode}, columns: L${leftSideColumns.length}/C${
                    centerColumns.length
                  }/R${rightSideColumns.length})`
                );
                title2ScrollData.lastLogTime = currentTime;
              }
            }
            title2ScrollData.lastGroupCenterX = groupCenterX;
          } else {
            // Fallback: No valid columns found for this group, use static position
            titleObject.position.x = title2ScrollData.target.position.x;
          }
        } else {
          // Fallback: No columns found for this group, use static position
          titleObject.position.x = title2ScrollData.target.position.x;
        }
      } else {
        // Fallback: No group index, use static position
        titleObject.position.x = title2ScrollData.target.position.x;
      }

      // Maintain Y and Z positions, scale, and rotation
      titleObject.position.y = title2ScrollData.target.position.y;
      titleObject.position.z = title2ScrollData.target.position.z;
      titleObject.scale.set(1, 1, 1);
      titleObject.rotation.set(0, 0, 0);
    }
  }

  /**
   * Check viewport and update category title2 scrolling state
   */
  checkCategoryTitle2ViewportAndUpdateScrolling() {
    if (this.categoryTitle2ScrollAnimationData.length === 0) {
      return;
    }

    // Use the same viewport bounds as grouped layout
    const needsScrolling = this.groupedLayoutBounds.totalWidth > this.groupedLayoutBounds.viewportWidth;

    if (needsScrolling !== this.isCategoryTitle2ScrollingEnabled) {
      this.isCategoryTitle2ScrollingEnabled = needsScrolling;
      console.log(`🎯 PHOTOS2 Header Animation: Category title2 scrolling ${needsScrolling ? "enabled" : "disabled"} based on viewport`);
    }
  }

  /**
   * 更新分组布局基于列的水平滚动动画，带无缝环绕
   */
  updateGroupedScrollAnimation() {
    if (this.groupedScrollAnimationData.length === 0) {
      // 如果没有动画数据，保持静态位置
      for (let i = 0; i < this.objects.length; i++) {
        const object = this.objects[i];
        const target = this.targets.grouped[i];
        if (target) {
          object.position.copy(target.position);
          object.scale.set(1, 1, 1);
          object.rotation.set(0, 0, 0);
        }
      }
      return;
    }

    // 定期检查是否应启用/禁用滚动
    const currentTime = Date.now();
    if (currentTime - this.lastViewportCheck > this.viewportCheckInterval) {
      this.checkGroupedViewportAndUpdateScrolling();
      this.lastViewportCheck = currentTime;
    }

    if (!this.isGroupedScrollingEnabled) {
      // 如果滚动被禁用，保持静态位置
      for (let i = 0; i < this.objects.length; i++) {
        const object = this.objects[i];
        const target = this.targets.grouped[i];
        if (target) {
          object.position.copy(target.position);
          object.scale.set(1, 1, 1);
          object.rotation.set(0, 0, 0);
        }
      }
      return;
    }

    // 更新每列的水平位置
    for (const columnData of this.groupedScrollAnimationData) {
      // 水平移动列
      columnData.currentX += columnData.speed;

      // 检查环绕 - 实现瞬间传送效果（类似GRID布局）
      if (columnData.currentX > columnData.wrapRightX) {
        // 查找最左侧列位置以进行无缝环绕
        let leftmostX = Infinity;
        let leftmostColumnData = null;

        for (const otherColumn of this.groupedScrollAnimationData) {
          if (otherColumn.columnIndex !== columnData.columnIndex && otherColumn.currentX > otherColumn.wrapLeftX && otherColumn.currentX < otherColumn.wrapRightX) {
            if (otherColumn.currentX < leftmostX) {
              leftmostX = otherColumn.currentX;
              leftmostColumnData = otherColumn;
            }
          }
        }

        // 计算考虑组间间距的无缝环绕位置
        let seamlessWrapX;

        if (leftmostColumnData) {
          // 检查当前列和最左侧列是否涉及不同的组
          const currentGroups = new Set(columnData.groupsInvolved);
          const leftmostGroups = new Set(leftmostColumnData.groupsInvolved);

          // 检查是否有组重叠
          const hasGroupOverlap = [...currentGroups].some((group) => leftmostGroups.has(group));

          // 根据组关系选择适当的间距
          let spacingToUse;
          if (hasGroupOverlap) {
            // 同组内的列，使用项目间距
            spacingToUse = columnData.itemSpacing;
          } else {
            // 不同组的列，需要考虑组间间距
            // 使用组间间距和项目间距的较大值，确保视觉分离
            spacingToUse = Math.max(columnData.groupGap, columnData.itemSpacing);
          }

          seamlessWrapX = leftmostX - spacingToUse;
        } else {
          // 如果未找到最左侧列，使用默认位置
          seamlessWrapX = columnData.wrapLeftX;
        }

        // 立即设置新位置（瞬间传送，无过渡动画）
        columnData.currentX = seamlessWrapX;

        // 对此列中的所有照片应用瞬间传送效果
        for (const photoIndex of columnData.photoIndices) {
          if (photoIndex < this.objects.length) {
            const object = this.objects[photoIndex];
            const target = this.targets.grouped[photoIndex];

            if (target && object) {
              // 临时禁用CSS过渡效果以确保即时定位
              const element = object.element;
              if (element) {
                element.classList.add("instant-position");
              }

              // 计算与列原始位置的偏移
              const offsetX = columnData.currentX - columnData.originalX;

              // 立即应用新位置（瞬间传送）
              object.position.x = target.position.x + offsetX;
              object.position.y = target.position.y; // 保持Y位置
              object.position.z = target.position.z; // 保持Z位置

              // 确保统一的缩放和旋转
              object.scale.set(1, 1, 1);
              object.rotation.set(0, 0, 0);

              // 在下一帧中移除即时位置类以恢复正常过渡效果
              requestAnimationFrame(() => {
                if (element) {
                  element.classList.remove("instant-position");
                }
              });
            }
          }
        }
      } else {
        // 正常滚动 - 更新此列中的所有照片位置（平滑移动）
        for (const photoIndex of columnData.photoIndices) {
          if (photoIndex < this.objects.length) {
            const object = this.objects[photoIndex];
            const target = this.targets.grouped[photoIndex];

            if (target && object) {
              // 计算与列原始位置的偏移
              const offsetX = columnData.currentX - columnData.originalX;

              // 应用列滚动，保持列内照片的相对位置
              object.position.x = target.position.x + offsetX;
              object.position.y = target.position.y; // 保持Y位置
              object.position.z = target.position.z; // 保持Z位置

              // 确保统一的缩放和旋转
              object.scale.set(1, 1, 1);
              object.rotation.set(0, 0, 0);
            }
          }
        }
      }
    }
  }

  /**
   * 检查分组布局视口并更新基于列的滚动动画状态
   */
  checkGroupedViewportAndUpdateScrolling() {
    // 重新计算视口宽度以防窗口被调整大小
    const camera = this.camera;
    const distance = Math.abs(camera.position.z);
    const vFOV = (camera.fov * Math.PI) / 180;
    const height = 2 * Math.tan(vFOV / 2) * distance;
    const viewportWidth = height * camera.aspect;

    // 更新存储的视口宽度
    this.groupedLayoutBounds.viewportWidth = viewportWidth;

    // 检查内容是否溢出视口并需要滚动
    const needsScrolling = this.groupedLayoutBounds.totalWidth > viewportWidth;
    this.isGroupedScrollingEnabled = needsScrolling;
  }

  /**
   * 生成随机布局
   */
  generateRandomLayout() {
    // 清除现有的随机布局和动画数据
    this.targets.random = [];
    this.randomAnimationData = [];

    // 扩展3D空间边界以创建更戏剧性的分布
    const bounds = {
      x: { min: -6000, max: 6000 }, // Expanded from ±5000 to ±6000 for wider spread
      y: { min: -1500, max: 1500 }, // Expanded from ±1000 to ±1500 for more vertical variety
      z: { min: -3000, max: -400 }, // Expanded from -2500/-500 to -3000/-400 for more depth variety
    };

    for (let i = 0; i < this.objects.length; i++) {
      const object = new THREE.Object3D();

      // 生成随机位置
      object.position.x = Math.random() * (bounds.x.max - bounds.x.min) + bounds.x.min;
      object.position.y = Math.random() * (bounds.y.max - bounds.y.min) + bounds.y.min;
      object.position.z = Math.random() * (bounds.z.max - bounds.z.min) + bounds.z.min;

      // ENHANCED: 为每个元素生成戏剧性动画数据 - Dramatic visual impact
      const animData = {
        basePosition: { x: object.position.x, y: object.position.y, z: object.position.z },
        // ENHANCED: More dramatic speed ranges for varied visual rhythm
        floatSpeed: 1.6 + Math.random() * 1.8, // 浮动速度：0.6-2.4 (wider range)
        floatAmplitude: this.floatingAmplitude + Math.random() * 100, // 浮动幅度：120-220 (much more dramatic)
        scaleSpeed: 0.4 + Math.random() * 1.4, // 缩放速度：0.4-1.8 (wider range)
        scaleAmplitude: this.scaleAmplitude + Math.random() * 0.15, // 缩放幅度：0.25-0.40 (more noticeable)
        phaseOffset: Math.random() * Math.PI * 2, // 动画去同步的相位偏移
        driftSpeed: 0.2 + Math.random() * 1.0, // 漂移速度：0.2-1.2 (wider range)
        driftAmplitude: this.driftAmplitude + Math.random() * 80, // 漂移幅度：100-180 (much more spatial movement)
        // 移除旋转参数，因为RANDOM模式不再使用旋转效果
      };

      this.randomAnimationData.push(animData);
      this.targets.random.push(object);
    }
  }

  /**
   * 生成分层的下落速度，创造明显的快慢差异
   */
  generateVariedFallSpeed() {
    const speedTiers = [
      { weight: 0.2, min: 30, max: 60 }, // 20% 慢速雨滴：30-60 像素/秒
      { weight: 0.3, min: 160, max: 120 }, // 30% 中速雨滴：80-120 像素/秒
      { weight: 0.3, min: 300, max: 180 }, // 30% 快速雨滴：140-180 像素/秒
      { weight: 0.2, min: 400, max: 280 }, // 20% 极速雨滴：200-280 像素/秒
    ];

    // 根据权重随机选择速度层级
    const random = Math.random();
    let cumulativeWeight = 0;

    for (const tier of speedTiers) {
      cumulativeWeight += tier.weight;
      if (random <= cumulativeWeight) {
        return tier.min + Math.random() * (tier.max - tier.min);
      }
    }

    // 默认返回中速（不应该到达这里）
    return 100;
  }

  /**
   * 生成瀑布雨布局 - Waterfall rain effect with continuous falling motion
   */
  generateRandom2Layout() {
    // 清除现有的瀑布雨布局和动画数据
    this.targets.random2 = [];
    this.random2AnimationData = [];

    // 瀑布雨: 3D空间边界设置
    const bounds = {
      x: { min: -5000, max: 5000 }, // 水平分布范围
      y: { min: 2500, max: 3500 }, // 起始高度（顶部）
      z: { min: -3000, max: -500 }, // 深度分布
    };

    // 瀑布雨: 下落区域设置
    const fallArea = {
      topY: 2500, // 雨滴重生的顶部位置
      bottomY: -2500, // 雨滴消失的底部位置
      totalHeight: 5000, // 总下落高度
    };

    for (let i = 0; i < this.objects.length; i++) {
      const object = new THREE.Object3D();

      // 瀑布雨: 随机分布在整个下落区域中
      object.position.x = Math.random() * (bounds.x.max - bounds.x.min) + bounds.x.min;
      object.position.y = Math.random() * (fallArea.topY - fallArea.bottomY) + fallArea.bottomY; // 随机分布在整个下落区域
      object.position.z = Math.random() * (bounds.z.max - bounds.z.min) + bounds.z.min;

      // 瀑布雨: 动画参数
      const animData = {
        // 固定的X和Z位置（雨滴垂直下落）
        fixedX: object.position.x,
        fixedZ: object.position.z,

        // 当前Y位置和下落参数
        currentY: object.position.y,
        fallSpeed: this.generateVariedFallSpeed(), // 使用分层速度生成更明显的快慢差异

        // 水平摆动效果（模拟风的影响）
        swayAmplitude: 30 + Math.random() * 50, // 摆动幅度：30-80像素
        swaySpeed: 0.5 + Math.random() * 1.0, // 摆动速度：0.5-1.5
        swayPhase: Math.random() * Math.PI * 2, // 摆动相位偏移

        // 缩放脉冲效果（模拟雨滴大小变化）
        scaleSpeed: 0.8 + Math.random() * 1.2, // 缩放速度：0.8-2.0
        scaleAmplitude: 0.1 + Math.random() * 0.15, // 缩放幅度：0.1-0.25
        scalePhase: Math.random() * Math.PI * 2, // 缩放相位偏移

        // 下落区域边界
        topBoundary: fallArea.topY,
        bottomBoundary: fallArea.bottomY,

        // 重生延迟（避免所有雨滴同时重生）
        respawnDelay: Math.random() * 2000, // 0-2秒的随机延迟
        lastRespawnTime: 0,
      };

      this.random2AnimationData.push(animData);
      this.targets.random2.push(object);
    }
  }

  /**
   * PERFORMANCE OPTIMIZED: 更新随机布局动画 - 60 FPS target with pre-computed values
   */
  updateRandomAnimation() {
    if (this.randomAnimationData.length === 0) {
      return;
    }
    // 如果动画被暂停（聚焦模式），不更新动画
    if (this.animationsPaused) {
      return;
    }

    const currentTime = Date.now();
    const currentTimeSeconds = currentTime * 0.001; // Convert to seconds

    // INTEGRATED FROM BACKUP: Full position regeneration every 5 seconds (more dramatic than position pool)
    if (currentTime - this.lastRandomUpdate > this.randomUpdateInterval && !this.isFocusMode) {
      // Use backup file approach: regenerate entire random layout for maximum variety
      this.generateRandomLayout();
      this.transform(this.targets.random, true); // FIXED: Enable smooth transitions for position changes
      this.lastRandomUpdate = currentTime;
    }

    // HYBRID ANIMATION SYSTEM: Combine backup file's real-time calculations with performance optimizations
    for (let i = 0; i < this.objects.length; i++) {
      const object = this.objects[i];
      const animData = this.randomAnimationData[i];

      if (!animData) {
        continue;
      }

      // ENHANCED: Dramatic visual effects with pre-computed values for 60 FPS performance
      // Calculate indices for lookup tables based on enhanced animation parameters (优化时间精度)
      const timeMultiplier = 40; // 统一时间精度倍数，提供更平滑的动画
      const floatIndex = Math.floor((currentTimeSeconds * animData.floatSpeed + animData.phaseOffset) * timeMultiplier) % this.animationLookupSize;
      const driftIndex = Math.floor((currentTimeSeconds * animData.driftSpeed + animData.phaseOffset * 0.7) * timeMultiplier) % this.animationLookupSize;
      const driftZIndex = Math.floor((currentTimeSeconds * animData.driftSpeed * 0.8 + animData.phaseOffset * 1.3) * timeMultiplier) % this.animationLookupSize;
      const scaleIndex = Math.floor((currentTimeSeconds * animData.scaleSpeed + animData.phaseOffset * 1.5) * timeMultiplier) % this.animationLookupSize;
      // 移除旋转相关的索引计算，因为不再需要旋转效果

      // ENHANCED: Dramatic floating effect (Y-axis) with increased amplitude multiplier
      const floatOffset = this.precomputedSines[floatIndex] * animData.floatAmplitude * 2.5; // Increased from 2 to 2.5

      // ENHANCED: Multi-axis drift for dramatic 3D spatial movement
      const driftOffsetX = this.precomputedSines[driftIndex] * animData.driftAmplitude * 2.2; // Increased from 2 to 2.2
      const driftOffsetZ = this.precomputedCosines[driftZIndex] * animData.driftAmplitude * 1.5; // Increased from 1 to 1.5

      // Apply dramatic position animations with enhanced 3D depth movement
      object.position.x = animData.basePosition.x + driftOffsetX;
      object.position.y = animData.basePosition.y + floatOffset;
      object.position.z = animData.basePosition.z + driftOffsetZ;

      // ENHANCED: More dramatic scale pulsing with increased amplitude
      const scaleOffset = this.precomputedSines[scaleIndex] * animData.scaleAmplitude;
      const scale = 1 + scaleOffset;
      object.scale.set(scale, scale, scale);

      // 移除旋转效果 - 保持对象直立
      object.rotation.set(0, 0, 0);
    }
  }

  /**
   * PERFORMANCE OPTIMIZED: 更新瀑布雨布局动画 - 60 FPS target with waterfall rain effect
   */
  updateRandom2Animation() {
    if (this.random2AnimationData.length === 0) {
      return;
    }
    // 如果动画被暂停（聚焦模式），不更新动画
    if (this.animationsPaused) {
      return;
    }

    const currentTime = Date.now();
    const currentTimeSeconds = currentTime * 0.001; // Convert to seconds
    const deltaTime = 1 / 60; // 假设60 FPS，用于计算位置增量

    // 瀑布雨: 定期重新生成布局以保持动态效果
    if (currentTime - this.lastRandom2Update > this.random2UpdateInterval && !this.isFocusMode) {
      // 重新生成瀑布雨布局
      this.generateRandom2Layout();
      this.transform(this.targets.random2, true); // 启用平滑过渡
      this.lastRandom2Update = currentTime;
    }

    // 瀑布雨: 更新每个雨滴的动画
    for (let i = 0; i < this.objects.length; i++) {
      const object = this.objects[i];
      const animData = this.random2AnimationData[i];

      if (!animData) {
        continue;
      }

      // 瀑布雨: 垂直下落运动
      animData.currentY -= animData.fallSpeed * deltaTime;

      // 瀑布雨: 检查是否需要重生（到达底部）
      if (animData.currentY < animData.bottomBoundary) {
        // 检查重生延迟
        if (currentTime - animData.lastRespawnTime > animData.respawnDelay) {
          // 临时禁用CSS过渡效果以确保瞬间重生到顶部
          const element = object.element;
          if (element) {
            element.classList.add("instant-position");
          }

          // 重生到顶部
          animData.currentY = animData.topBoundary;
          animData.lastRespawnTime = currentTime;

          // 随机化一些参数以增加变化
          animData.fallSpeed = this.generateVariedFallSpeed(); // 重生时也使用分层速度生成
          animData.swayPhase = Math.random() * Math.PI * 2;
          animData.scalePhase = Math.random() * Math.PI * 2;

          // 在下一帧中移除即时位置类以恢复正常过渡效果
          requestAnimationFrame(() => {
            if (element) {
              element.classList.remove("instant-position");
            }
          });
        } else {
          // 等待重生，暂时隐藏（移到屏幕外）
          animData.currentY = animData.bottomBoundary - 1000;
        }
      }

      // 瀑布雨: 计算水平摆动（模拟风的影响）
      const swayIndex = Math.floor((currentTimeSeconds * animData.swaySpeed + animData.swayPhase) * 40) % this.animationLookupSize;
      const swayOffset = this.precomputedSines[swayIndex] * animData.swayAmplitude;

      // 瀑布雨: 计算缩放脉冲（模拟雨滴大小变化）
      const scaleIndex = Math.floor((currentTimeSeconds * animData.scaleSpeed + animData.scalePhase) * 40) % this.animationLookupSize;
      const scaleOffset = this.precomputedSines[scaleIndex] * animData.scaleAmplitude;
      const scale = 1 + scaleOffset;

      // 瀑布雨: 应用位置和变换
      object.position.x = animData.fixedX + swayOffset; // 固定X位置 + 摆动
      object.position.y = animData.currentY; // 当前下落Y位置
      object.position.z = animData.fixedZ; // 固定Z位置

      // 瀑布雨: 应用缩放效果
      object.scale.set(scale, scale, scale);

      // 瀑布雨: 保持对象直立（无旋转）
      object.rotation.set(0, 0, 0);
    }
  }

  /**
   * 更新网格布局滚动动画，带无缝环绕
   */
  updateGridScrollAnimation() {
    // 如果处于聚焦模式或动画被暂停（包括聚焦退出动画期间），暂停所有动画
    if (this.isFocusMode || this.animationsPaused) {
      return;
    }

    // 如果正在执行网格飞入动画，更新飞入动画而不是滚动动画
    if (this.isGridFlyInActive) {
      this.updateGridFlyInAnimation();
      return;
    }

    if (this.gridScrollAnimationData.length === 0) {
      return;
    }

    // 定期检查视口边界并决定是否启用滚动动画
    const currentTime = Date.now();
    if (currentTime - this.lastViewportCheck > this.viewportCheckInterval) {
      this.checkViewportAndUpdateScrolling();
      this.lastViewportCheck = currentTime;
    }

    // 如果滚动动画被禁用，跳过位置更新
    if (!this.isScrollingEnabled) {
      // 仍然确保所有对象保持统一的缩放和旋转
      for (let i = 0; i < this.objects.length; i++) {
        const object = this.objects[i];
        object.scale.set(1, 1, 1);
        object.rotation.set(0, 0, 0);
      }

      return;
    }

    // 首先，按行分组所有对象以进行行级环绕计算
    // 动态创建行组以支持不同的行数（3行）
    const maxRows = Math.max(...this.gridScrollAnimationData.map((data) => data.row)) + 1;
    const rowGroups = {};
    for (let i = 0; i < maxRows; i++) {
      rowGroups[i] = [];
    }

    for (let i = 0; i < this.objects.length; i++) {
      const scrollData = this.gridScrollAnimationData[i];
      if (scrollData) {
        rowGroups[scrollData.row].push({ index: i, scrollData, object: this.objects[i] });
      }
    }

    // 处理每一行 - 使用动态计算的行数而不是硬编码的3行
    for (let rowIndex = 0; rowIndex < maxRows; rowIndex++) {
      const rowItems = rowGroups[rowIndex];
      if (rowItems.length === 0) continue;

      // 更新每个对象的X位置
      for (const item of rowItems) {
        item.scrollData.currentX += item.scrollData.speed;
      }

      // 查找当前行中最左侧可见图像位置
      let leftmostVisibleX = Infinity;
      let rightmostX = -Infinity;
      let defaultWrapLeftX = rowItems.length > 0 ? rowItems[0].scrollData.wrapLeftX : -1000;

      for (const item of rowItems) {
        // 仅考虑在合理可见范围内的图像来确定最左侧位置
        if (item.scrollData.currentX > item.scrollData.wrapLeftX && item.scrollData.currentX < item.scrollData.wrapRightX) {
          leftmostVisibleX = Math.min(leftmostVisibleX, item.scrollData.currentX);
        }
        rightmostX = Math.max(rightmostX, item.scrollData.currentX);
      }

      // 如果未找到可见的最左侧图像，使用默认值
      if (leftmostVisibleX === Infinity) {
        leftmostVisibleX = defaultWrapLeftX;
      }

      // 检查需要环绕的对象并执行无缝环绕
      for (const item of rowItems) {
        if (item.scrollData.currentX > item.scrollData.wrapRightX) {
          // 调试信息：输出环绕触发
          if (this.isPhotos5Mode) {
            console.log(`🎯 PHOTOS5 Wrap Triggered: currentX=${item.scrollData.currentX}, wrapRightX=${item.scrollData.wrapRightX}`);
          }
          // 计算无缝环绕位置：放置在当前最左侧可见图像的左侧
          // PHOTOS3和PHOTOS5模式使用更紧凑的列间距
          let columnSpacing = 650; // 默认列间距
          if (this.isPhotos3Mode) {
            columnSpacing = 450; // PHOTOS3模式
          } else if (this.isPhotos5Mode) {
            columnSpacing = 700; // PHOTOS5模式
          }
          const seamlessWrapX = leftmostVisibleX - columnSpacing;

          // 临时禁用CSS过渡效果以确保即时定位
          const element = item.object.element;
          if (element) {
            element.classList.add("instant-position");
          }

          // 立即设置新位置
          item.scrollData.currentX = seamlessWrapX;
          item.object.position.x = seamlessWrapX;
          item.object.position.y = item.scrollData.baseY;
          item.object.position.z = item.scrollData.baseZ;

          // 确保统一的缩放和旋转（重置随机布局的任何效果）
          item.object.scale.set(1, 1, 1);
          item.object.rotation.set(0, 0, 0);

          // 在下一帧中移除即时位置类以恢复正常过渡效果
          requestAnimationFrame(() => {
            if (element) {
              element.classList.remove("instant-position");
            }
          });
        } else {
          // 正常滚动 - 应用新位置但保持网格Y和Z位置
          item.object.position.x = item.scrollData.currentX;
          item.object.position.y = item.scrollData.baseY; // 保持网格行位置
          item.object.position.z = item.scrollData.baseZ; // 保持网格深度位置
        }

        // 确保所有网格对象具有统一的缩放（重置随机布局的任何缩放效果）
        item.object.scale.set(1, 1, 1);
        item.object.rotation.set(0, 0, 0);
      }
    }
  }

  /**
   * 更新翻转网格布局垂直滚动动画，带无缝环绕
   */
  updateFlipGridScrollAnimation() {
    // 如果处于聚焦模式或动画被暂停（包括聚焦退出动画期间），暂停所有动画
    if (this.isFocusMode || this.animationsPaused) {
      return;
    }

    if (this.flipGridScrollAnimationData.length === 0) {
      return;
    }

    // 定期检查视口边界并决定是否启用滚动动画
    const currentTime = Date.now();
    if (currentTime - this.lastViewportCheck > this.viewportCheckInterval) {
      this.checkViewportAndUpdateScrolling();
      this.lastViewportCheck = currentTime;
    }

    // 如果滚动动画被禁用，跳过位置更新
    if (!this.isScrollingEnabled) {
      // 仍然确保所有对象保持统一的缩放和旋转
      for (let i = 0; i < this.objects.length; i++) {
        const object = this.objects[i];
        object.scale.set(1, 1, 1);
        object.rotation.set(0, 0, 0);
      }

      return;
    }

    // 首先，按列分组所有对象以进行列级环绕计算
    // 动态创建列组以支持不同的列数（3列或5列）
    const maxCols = Math.max(...this.flipGridScrollAnimationData.map((data) => data.col)) + 1;
    const colGroups = {};
    for (let i = 0; i < maxCols; i++) {
      colGroups[i] = [];
    }

    for (let i = 0; i < this.objects.length; i++) {
      const scrollData = this.flipGridScrollAnimationData[i];
      if (scrollData) {
        colGroups[scrollData.col].push({ index: i, scrollData, object: this.objects[i] });
      }
    }

    // 处理每一列 - 使用动态计算的列数而不是硬编码的3列
    for (let colIndex = 0; colIndex < maxCols; colIndex++) {
      const colItems = colGroups[colIndex];
      if (colItems.length === 0) continue;

      // 更新每个对象的Y位置
      for (const item of colItems) {
        item.scrollData.currentY += item.scrollData.speed;
      }

      // 查找当前列中最上方可见图像位置
      let topmostVisibleY = -Infinity;
      let bottommostY = Infinity;
      let defaultWrapTopY = colItems.length > 0 ? colItems[0].scrollData.wrapTopY : 1000;

      for (const item of colItems) {
        // 仅考虑在合理可见范围内的图像来确定最上方位置
        if (item.scrollData.currentY < item.scrollData.wrapTopY && item.scrollData.currentY > item.scrollData.wrapBottomY) {
          topmostVisibleY = Math.max(topmostVisibleY, item.scrollData.currentY);
        }
        bottommostY = Math.min(bottommostY, item.scrollData.currentY);
      }

      // 如果未找到可见的最上方图像，使用默认值
      if (topmostVisibleY === -Infinity) {
        topmostVisibleY = defaultWrapTopY;
      }

      // 检查需要环绕的对象并执行无缝环绕
      for (const item of colItems) {
        if (item.scrollData.currentY < item.scrollData.wrapBottomY) {
          // 计算无缝环绕位置：放置在当前最上方可见图像的上方
          // 使用与网格布局相似的行间距配置，但针对垂直布局进行调整
          let rowSpacing = 450; // 默认行间距
          if (this.isPhotos3Mode) {
            rowSpacing = 350; // PHOTOS3模式
          } else if (this.isPhotos5Mode) {
            rowSpacing = 300; // PHOTOS5模式
          }
          const seamlessWrapY = topmostVisibleY + rowSpacing;

          // 临时禁用CSS过渡效果以确保即时定位
          const element = item.object.element;
          if (element) {
            element.classList.add("instant-position");
          }

          // 立即设置新位置
          item.scrollData.currentY = seamlessWrapY;
          item.object.position.x = item.scrollData.baseX;
          item.object.position.y = seamlessWrapY;
          item.object.position.z = item.scrollData.baseZ;

          // 确保统一的缩放和旋转（重置随机布局的任何效果）
          item.object.scale.set(1, 1, 1);
          item.object.rotation.set(0, 0, 0);

          // 在下一帧中移除即时位置类以恢复正常过渡效果
          requestAnimationFrame(() => {
            if (element) {
              element.classList.remove("instant-position");
            }
          });
        } else {
          // 正常滚动 - 应用新位置但保持网格X和Z位置
          item.object.position.x = item.scrollData.baseX; // 保持网格列位置
          item.object.position.y = item.scrollData.currentY;
          item.object.position.z = item.scrollData.baseZ; // 保持网格深度位置
        }

        // 确保所有翻转网格对象具有统一的缩放（重置随机布局的任何缩放效果）
        item.object.scale.set(1, 1, 1);
        item.object.rotation.set(0, 0, 0);
      }
    }
  }

  /**
   * 基于图像可见性检查视口并更新滚动动画状态
   */
  checkViewportAndUpdateScrolling() {
    // 计算当前视口边界（基于固定相机位置和视野）
    // 触发条件：窗口调整大小、布局切换、过渡完成、定期检查
    this.calculateViewportBounds();

    // 检查是否有需要动画的图像（基于部分可见性阈值）
    const hasImagesNeedingAnimation = this.detectImagesOutsideViewport();

    // 基于检测结果更新滚动状态
    this.isScrollingEnabled = hasImagesNeedingAnimation;
  }

  /**
   * 基于相机参数计算视口边界
   */
  calculateViewportBounds() {
    // 获取相机视野参数
    const camera = this.camera;
    const distance = Math.abs(camera.position.z); // 相机到场景的距离

    // 计算垂直和水平视野范围
    const vFOV = (camera.fov * Math.PI) / 180; // 垂直视野角度转弧度
    const height = 2 * Math.tan(vFOV / 2) * distance; // 视口高度
    const width = height * camera.aspect; // 视口宽度

    // 考虑相机位置偏移，计算实际视口边界
    const centerX = camera.position.x;
    const centerY = camera.position.y;

    // 使用较小的缓冲区以提高部分可见性检测精度
    const bufferX = width * this.viewportBufferMultiplier; // 5%缓冲区（减少以提高精度）
    const bufferY = height * this.viewportBufferMultiplier; // 5%缓冲区（减少以提高精度）

    this.viewportBounds = {
      left: centerX - width / 2 - bufferX,
      right: centerX + width / 2 + bufferX,
      top: centerY + height / 2 + bufferY,
      bottom: centerY - height / 2 - bufferY,
    };
  }

  /**
   * 计算图像在视口内的可见性百分比
   */
  calculateImageVisibilityPercentage(imageX, imageY, imageWidth, imageHeight) {
    // 计算图像边界
    const imageLeft = imageX - imageWidth / 2;
    const imageRight = imageX + imageWidth / 2;
    const imageTop = imageY + imageHeight / 2;
    const imageBottom = imageY - imageHeight / 2;

    // 计算图像与视口之间的交集区域
    const intersectionLeft = Math.max(imageLeft, this.viewportBounds.left);
    const intersectionRight = Math.min(imageRight, this.viewportBounds.right);
    const intersectionTop = Math.min(imageTop, this.viewportBounds.top);
    const intersectionBottom = Math.max(imageBottom, this.viewportBounds.bottom);

    // 如果没有交集，返回0%可见
    if (intersectionLeft >= intersectionRight || intersectionBottom >= intersectionTop) {
      return 0;
    }

    // 计算交集面积
    const intersectionWidth = intersectionRight - intersectionLeft;
    const intersectionHeight = intersectionTop - intersectionBottom;
    const intersectionArea = intersectionWidth * intersectionHeight;

    // 计算总图像面积
    const totalImageArea = imageWidth * imageHeight;

    // 返回可见性百分比（0-1）
    return intersectionArea / totalImageArea;
  }

  /**
   * 检测视口外需要动画的图像
   */
  detectImagesOutsideViewport() {
    let imagesNeedingAnimation = 0;
    let totalImages = 0;
    let partiallyVisibleCount = 0;
    let fullyVisibleCount = 0;
    let fullyHiddenCount = 0;

    for (let i = 0; i < this.objects.length; i++) {
      const object = this.objects[i];
      const scrollData = this.gridScrollAnimationData[i];

      if (!scrollData) continue;

      totalImages++;

      // 获取图像的当前位置
      const imageX = object.position.x;
      const imageY = object.position.y;

      // 图像尺寸（用于更精确的边界检测）
      // 检测模板类型以使用正确的尺寸
      let imageWidth = 300; // 默认图像宽度
      let imageHeight = 400; // 默认图像高度

      // 检查是否为PHOTOS 5模板（使用不同的尺寸）
      if (object.element && object.element.classList.contains("photo-frame-photos5")) {
        imageWidth = 300; // PHOTOS 5模板宽度
        imageHeight = 254; // PHOTOS 5模板高度（更紧凑）
      }

      // 计算图像可见性百分比
      const visibilityPercentage = this.calculateImageVisibilityPercentage(imageX, imageY, imageWidth, imageHeight);

      // 基于可见性百分比对图像状态进行分类
      if (visibilityPercentage === 0) {
        fullyHiddenCount++;
        imagesNeedingAnimation++; // 完全隐藏的图像需要动画来显示
      } else if (visibilityPercentage < this.partialVisibilityThreshold) {
        partiallyVisibleCount++;
        imagesNeedingAnimation++; // 部分可见但低于阈值的图像需要动画
      } else {
        fullyVisibleCount++; // 充分可见的图像不需要额外动画
      }
    }

    // 如果有需要动画的图像（完全隐藏或部分可见性低于阈值），启用滚动
    const hasImagesNeedingAnimation = imagesNeedingAnimation > 0;

    return hasImagesNeedingAnimation;
  }

  /**
   * 在运行时设置部分可见性阈值
   */
  setPartialVisibilityThreshold(threshold) {
    // 确保阈值在合理范围内（0.1到0.8）
    this.partialVisibilityThreshold = Math.max(0.1, Math.min(0.8, threshold));

    // 立即重新检查视口状态以应用新阈值
    if (this.currentLayout === "grid" || this.currentLayout === "flipGrid") {
      this.checkViewportAndUpdateScrolling();
    }
  }

  /**
   * 在运行时设置分组布局列滚动的统一速度
   */
  setGroupedColumnScrollSpeed(speed) {
    // 确保速度在合理范围内（0.1到5.0）
    this.groupedColumnScrollSpeed = Math.max(0.1, Math.min(5.0, speed));

    // 如果当前是分组布局且有滚动动画数据，立即更新所有列的速度
    if (this.currentLayout === "grouped" && this.groupedScrollAnimationData.length > 0) {
      this.groupedScrollAnimationData.forEach((columnData) => {
        columnData.speed = this.groupedColumnScrollSpeed;
      });
    }
  }

  /**
   * 将对象转换到目标布局位置
   */
  transform(targets, enableTransition = true) {
    // 如果需要，存储当前位置以进行平滑过渡
    if (enableTransition && this.currentLayout === "grid") {
      this.setupSmoothGridTransition(targets);
    } else if (enableTransition && this.currentLayout === "flipGrid") {
      this.setupSmoothFlipGridTransition(targets);
    } else if (enableTransition && this.currentLayout === "random") {
      // ADDED: Smooth transitions for RANDOM layout position changes
      this.setupSmoothRandomTransition(targets);
    } else if (enableTransition && this.currentLayout === "random2") {
      // ADDED: Smooth transitions for RANDOM2 layout position changes
      this.setupSmoothRandom2Transition(targets);
    } else {
      // 用于即时定位或其他布局（使用自己的动画系统）
      for (let i = 0; i < this.objects.length; i++) {
        const object = this.objects[i];
        const target = targets[i];

        if (!target) {
          continue;
        }

        // 直接设置位置
        object.position.copy(target.position);
        object.scale.set(1, 1, 1);
        object.rotation.set(0, 0, 0);
      }
    }

    this.renderCallback();
  }

  /**
   * 设置到随机布局的平滑过渡
   */
  setupSmoothRandomTransition(targets) {
    // Store current positions as transition starting points
    const transitionData = [];
    for (let i = 0; i < this.objects.length; i++) {
      const object = this.objects[i];
      const target = targets[i];

      if (!target) {
        continue;
      }

      transitionData.push({
        startPos: { x: object.position.x, y: object.position.y, z: object.position.z },
        endPos: { x: target.position.x, y: target.position.y, z: target.position.z },
        object: object,
      });
    }

    // Start transition animation to new random positions
    this.animateToRandom(transitionData);
  }

  /**
   * 设置到瀑布雨布局的平滑过渡
   */
  setupSmoothRandom2Transition(targets) {
    // Store current positions as transition starting points
    const transitionData = [];
    for (let i = 0; i < this.objects.length; i++) {
      const object = this.objects[i];
      const target = targets[i];

      if (!target) {
        continue;
      }

      transitionData.push({
        startPos: { x: object.position.x, y: object.position.y, z: object.position.z },
        endPos: { x: target.position.x, y: target.position.y, z: target.position.z },
        object: object,
      });
    }

    // Start transition animation to new waterfall rain positions
    this.animateToRandom2(transitionData);
  }

  /**
   * 设置到网格布局的平滑过渡
   */
  setupSmoothGridTransition(targets) {
    // 存储当前位置作为过渡的起始点
    const transitionData = [];
    for (let i = 0; i < this.objects.length; i++) {
      const object = this.objects[i];
      const target = targets[i];

      transitionData.push({
        startPos: { x: object.position.x, y: object.position.y, z: object.position.z },
        endPos: { x: target.position.x, y: target.position.y, z: target.position.z },
        object: object,
      });
    }

    // 开始过渡动画
    this.animateToGrid(transitionData);
  }

  /**
   * 设置到翻转网格布局的平滑过渡
   */
  setupSmoothFlipGridTransition(targets) {
    // 存储当前位置作为过渡的起始点
    const transitionData = [];
    for (let i = 0; i < this.objects.length; i++) {
      const object = this.objects[i];
      const target = targets[i];

      transitionData.push({
        startPos: { x: object.position.x, y: object.position.y, z: object.position.z },
        endPos: { x: target.position.x, y: target.position.y, z: target.position.z },
        object: object,
      });
    }

    // 开始过渡动画
    this.animateToFlipGrid(transitionData);
  }

  /**
   * 动画化到随机布局的平滑过渡
   */
  animateToRandom(transitionData) {
    const duration = 800; // 0.8秒 - 与网格布局过渡时间一致
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用缓动函数进行平滑过渡
      const easeProgress = this.easeInOutCubic(progress);

      for (const data of transitionData) {
        const { startPos, endPos, object } = data;

        // 在起始和结束位置之间插值
        object.position.x = startPos.x + (endPos.x - startPos.x) * easeProgress;
        object.position.y = startPos.y + (endPos.y - startPos.y) * easeProgress;
        object.position.z = startPos.z + (endPos.z - startPos.z) * easeProgress;

        // 在过渡期间确保统一缩放和旋转（为随机动画做准备）
        object.scale.set(1, 1, 1);
        object.rotation.set(0, 0, 0);
      }

      this.renderCallback();

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        // 过渡完成后，随机动画系统将接管位置控制
        // 不需要额外的初始化，因为随机动画会在下一帧开始
      }
    };

    animate();
  }

  /**
   * 动画化到瀑布雨布局的平滑过渡
   */
  animateToRandom2(transitionData) {
    const duration = 800; // 0.8秒 - 与其他布局过渡时间一致
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用缓动函数进行平滑过渡
      const easeProgress = this.easeInOutCubic(progress);

      for (const data of transitionData) {
        const { startPos, endPos, object } = data;

        // 在起始和结束位置之间插值
        object.position.x = startPos.x + (endPos.x - startPos.x) * easeProgress;
        object.position.y = startPos.y + (endPos.y - startPos.y) * easeProgress;
        object.position.z = startPos.z + (endPos.z - startPos.z) * easeProgress;

        // 在过渡期间确保统一缩放和旋转（为瀑布雨动画做准备）
        object.scale.set(1, 1, 1);
        object.rotation.set(0, 0, 0);
      }

      this.renderCallback();

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        // 过渡完成后，瀑布雨动画系统将接管位置控制
        // 不需要额外的初始化，因为瀑布雨动画会在下一帧开始
      }
    };

    animate();
  }

  /**
   * 动画化到网格布局的平滑过渡
   */
  animateToGrid(transitionData) {
    const duration = 800; // 0.8秒
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用缓动函数进行平滑过渡
      const easeProgress = this.easeInOutCubic(progress);

      for (const data of transitionData) {
        const { startPos, endPos, object } = data;

        // 在起始和结束位置之间插值
        object.position.x = startPos.x + (endPos.x - startPos.x) * easeProgress;
        object.position.y = startPos.y + (endPos.y - startPos.y) * easeProgress;
        object.position.z = startPos.z + (endPos.z - startPos.z) * easeProgress;

        // 在过渡期间确保统一缩放（重置任何随机布局缩放）
        object.scale.set(1, 1, 1);
        object.rotation.set(0, 0, 0);
      }

      this.renderCallback();

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        // 过渡完成后初始化网格滚动动画数据
        this.initializeGridScrollPositions();
        // 过渡完成后立即检查视口状态
        this.checkViewportAndUpdateScrolling();
      }
    };

    animate();
  }

  /**
   * 动画化到翻转网格布局的平滑过渡
   */
  animateToFlipGrid(transitionData) {
    const duration = 800; // 0.8秒
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用缓动函数进行平滑过渡
      const easeProgress = this.easeInOutCubic(progress);

      for (const data of transitionData) {
        const { startPos, endPos, object } = data;

        // 在起始和结束位置之间插值
        object.position.x = startPos.x + (endPos.x - startPos.x) * easeProgress;
        object.position.y = startPos.y + (endPos.y - startPos.y) * easeProgress;
        object.position.z = startPos.z + (endPos.z - startPos.z) * easeProgress;

        // 在过渡期间确保统一缩放（重置任何随机布局缩放）
        object.scale.set(1, 1, 1);
        object.rotation.set(0, 0, 0);
      }

      this.renderCallback();

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        // 过渡完成后初始化翻转网格滚动动画数据
        this.initializeFlipGridScrollPositions();
        // 过渡完成后立即检查视口状态
        this.checkViewportAndUpdateScrolling();
      }
    };

    animate();
  }

  /**
   * 过渡后初始化翻转网格滚动位置
   */
  initializeFlipGridScrollPositions() {
    // 使用当前位置初始化翻转网格滚动动画数据
    for (let i = 0; i < this.objects.length; i++) {
      if (this.flipGridScrollAnimationData[i]) {
        this.flipGridScrollAnimationData[i].currentY = this.objects[i].position.y;
      }
    }
  }

  /**
   * 用于平滑过渡的三次缓动函数
   */
  easeInOutCubic(t) {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  }

  /**
   * 用于PHOTOS4的四次缓动函数（更戏剧性的效果）
   */
  easeInOutQuart(t) {
    return t < 0.5 ? 8 * t * t * t * t : 1 - Math.pow(-2 * t + 2, 4) / 2;
  }

  /**
   * 过渡后初始化网格滚动位置
   */
  initializeGridScrollPositions() {
    // 使用当前位置初始化网格滚动动画数据
    for (let i = 0; i < this.objects.length; i++) {
      if (this.gridScrollAnimationData[i]) {
        this.gridScrollAnimationData[i].currentX = this.objects[i].position.x;
      }
    }
  }

  /**
   * 将特定于布局的CSS类应用于元素
   */
  applyLayoutClasses(layout) {
    if (layout === "switching") {
      // 为过渡应用切换类
      for (let i = 0; i < this.objects.length; i++) {
        const object = this.objects[i];
        const element = object.element;
        if (element) {
          element.classList.remove("layout-grid", "layout-random", "layout-random2", "layout-grouped", "layout-grouped4");
          element.classList.add("layout-switching");
        }
      }
    } else {
      // 应用特定布局类
      for (let i = 0; i < this.objects.length; i++) {
        const object = this.objects[i];
        const element = object.element;
        if (element) {
          element.classList.remove("layout-grid", "layout-random", "layout-random2", "layout-grouped", "layout-grouped4", "layout-switching");
          element.classList.add(`layout-${layout}`);
        }
      }
    }
  }

  /**
   * 处理图像点击事件（在随机模式、RANDOM2模式、PHOTOS1模式、PHOTOS2模式、PHOTOS4模式和flipGrid模式下激活）
   */
  handleImageClick(imageIndex, clickedObject) {
    // 在随机模式、RANDOM2模式、网格模式（PHOTOS1）、翻转网格模式、PHOTOS2模式和PHOTOS4模式下启用点击聚焦功能
    if (
      this.currentLayout !== "random" &&
      this.currentLayout !== "random2" &&
      this.currentLayout !== "grid" &&
      this.currentLayout !== "flipGrid" &&
      this.currentLayout !== "grouped" &&
      this.currentLayout !== "grouped4"
    ) {
      return;
    }

    // 如果已经在聚焦模式
    if (this.isFocusMode) {
      // PHOTOS2 mode: Implement sticky focus behavior
      if (this.isPhotos2Mode && this.currentLayout === "grouped") {
        // In PHOTOS2 mode, clicking on any element (same or different) should not exit focus
        // Focus can only be cleared via the return button
        console.log("🔒 PHOTOS2 Sticky Focus: Click ignored, use return button to exit focus");
        return;
      }

      // PHOTOS4 mode: Implement sticky focus behavior
      if (this.isPhotos4Mode && this.currentLayout === "grouped4") {
        // In PHOTOS4 mode, clicking on any element (same or different) should not exit focus
        // Focus can only be cleared via the return button
        console.log("🔒 PHOTOS4 Sticky Focus: Click ignored, use return button to exit focus");
        return;
      }

      // For other modes: original behavior
      if (this.focusedObject === clickedObject) {
        // RANDOM和FLIP GRID模式特殊处理：点击聚焦元素本身不退出聚焦模式
        if (this.currentLayout === "random" || this.currentLayout === "flipGrid") {
          console.log(`🎯 ${this.currentLayout.toUpperCase()} Mode: Clicked on focused object, staying in focus mode`);
          return;
        }
        // 其他模式：如果点击了同一对象，退出聚焦模式
        this.exitFocusMode();
        return;
      } else {
        // RANDOM和FLIP GRID模式特殊处理：点击任何非聚焦元素都退出聚焦模式
        if (this.currentLayout === "random" || this.currentLayout === "flipGrid") {
          console.log(`🎯 ${this.currentLayout.toUpperCase()} Mode: Clicked on different object, exiting focus mode`);
          this.exitFocusMode();
          return;
        }
        // 其他模式：如果点击了不同的对象，阻止新的聚焦并提示用户
        return;
      }
    }

    // 进入聚焦模式
    this.enterFocusMode(imageIndex, clickedObject);
  }

  /**
   * 进入聚焦模式（随机布局、RANDOM2布局、网格布局（PHOTOS1）、翻转网格布局和PHOTOS2布局）
   */
  enterFocusMode(imageIndex, focusedObject) {
    console.log("🎯 enterFocusMode: Starting focus mode", {
      imageIndex,
      focusedObject: focusedObject ? "exists" : "null",
      currentLayout: this.currentLayout,
      wasAlreadyInFocus: this.isFocusMode,
    });

    this.isFocusMode = true;
    this.focusedObject = focusedObject;
    this.isFocusAnimationComplete = false; // 重置动画完成标志
    this.pendingFocusExit = false; // 清除任何待处理的退出请求
    this.animationsPaused = true; // 暂停所有动画

    // 在不同布局模式下暂停相应的动画
    if (this.currentLayout === "grid") {
      this.isGridFlyInActive = false; // 暂停网格飞入动画
    } else if (this.currentLayout === "flipGrid") {
      // 翻转网格模式不需要特殊的飞入动画暂停，只需要暂停滚动动画
    } else if (this.currentLayout === "grouped") {
      this.isGroupedFlyInActive = false; // 暂停飞入动画
    } else if (this.currentLayout === "grouped4") {
      this.isGrouped4FlyInActive = false; // 暂停PHOTOS4飞入动画
    }

    // 保存所有对象的当前位置
    this.originalPositions = [];
    for (let i = 0; i < this.objects.length; i++) {
      const obj = this.objects[i];
      this.originalPositions.push({
        x: obj.position.x,
        y: obj.position.y,
        z: obj.position.z,
        scale: { x: obj.scale.x, y: obj.scale.y, z: obj.scale.z },
      });
    }

    // 淡出其他元素 - 根据布局类型使用不同的CSS类
    for (let i = 0; i < this.objects.length; i++) {
      const obj = this.objects[i];
      const element = obj.element;

      if (obj !== focusedObject && element) {
        element.classList.add("focus-fade-out");
      }
    }

    // 淡出header元素 - 根据当前布局模式
    if (this.currentLayout === "grouped") {
      // PHOTOS2 mode: fade out category title2 headers
      for (let i = 0; i < this.categoryTitle2Objects.length; i++) {
        const titleObj = this.categoryTitle2Objects[i];
        const titleElement = titleObj.element;
        if (titleElement) {
          titleElement.classList.add("focus-fade-out");
        }
      }
    } else if (this.currentLayout === "grouped4") {
      // PHOTOS4 mode: fade out category title headers
      for (let i = 0; i < this.categoryTitleObjects.length; i++) {
        const titleObj = this.categoryTitleObjects[i];
        const titleElement = titleObj.element;
        if (titleElement) {
          titleElement.classList.add("focus-fade-out");
        }
      }
    }

    // 将聚焦对象移动到相机前方和中心
    const focusedElement = focusedObject.element;
    if (focusedElement) {
      focusedElement.classList.add("focus-centered");
    }

    // 平滑地将聚焦对象移动到中心位置
    this.animateToFocusPosition(focusedObject);

    // 安全网：如果动画因某种原因未能完成，设置一个超时来强制完成
    const animationDuration = this.currentLayout === "grouped" || this.currentLayout === "grouped4" ? 700 : 600;
    setTimeout(() => {
      if (this.isFocusMode && !this.isFocusAnimationComplete) {
        console.warn("⚠️ Focus animation timeout reached, forcing completion");
        this.isFocusAnimationComplete = true;
        if (this.pendingFocusExit) {
          console.log("🔄 Executing pending focus exit after timeout");
          this.pendingFocusExit = false;
          this.exitFocusMode();
        }
      }
    }, animationDuration + 100); // 额外100ms缓冲

    // 调用聚焦模式进入回调
    if (this.focusCallbacks.onEnterFocus) {
      this.focusCallbacks.onEnterFocus(imageIndex, focusedObject, this.currentLayout);
    }
  }

  /**
   * 退出聚焦模式
   */
  exitFocusMode() {
    if (!this.isFocusMode) {
      console.log("🔍 exitFocusMode: Not in focus mode, ignoring exit request");
      return;
    }

    // 防止重复退出请求
    if (this.isExitingFocus) {
      console.log("🔍 exitFocusMode: Already exiting focus mode, ignoring duplicate request");
      return;
    }

    // 检查聚焦进入动画是否完成
    if (!this.isFocusAnimationComplete) {
      console.log("⏳ exitFocusMode: Focus entry animation still in progress, queuing exit request", {
        currentLayout: this.currentLayout,
        focusedObject: this.focusedObject ? "exists" : "null",
        animationComplete: this.isFocusAnimationComplete,
        alreadyPending: this.pendingFocusExit,
      });
      this.pendingFocusExit = true;
      return;
    }

    console.log("✅ exitFocusMode: Animation complete, proceeding with exit", {
      currentLayout: this.currentLayout,
      animationComplete: this.isFocusAnimationComplete,
      hadPendingExit: this.pendingFocusExit,
    });
    // 设置退出标志
    this.isExitingFocus = true;

    // 额外的安全检查：确保focusedObject存在
    if (!this.focusedObject) {
      console.warn("⚠️ exitFocusMode: focusedObject is null, performing immediate cleanup");
      // 直接清理状态而不尝试动画
      this.isFocusMode = false;
      this.isExitingFocus = false;
      this.animationsPaused = false;
      this.originalPositions = [];

      // 调用退出回调
      if (this.focusCallbacks.onExitFocus) {
        const wasGroupedLayout = this.currentLayout === "grouped" || this.currentLayout === "grouped4";
        const wasGridLayout = this.currentLayout === "grid" || this.currentLayout === "flipGrid";
        this.focusCallbacks.onExitFocus(wasGroupedLayout || wasGridLayout);
      }
      return;
    }

    console.log("🔍 exitFocusMode: Starting focus exit animation");
    const wasGroupedLayout = this.currentLayout === "grouped" || this.currentLayout === "grouped4";
    const wasGridLayout = this.currentLayout === "grid" || this.currentLayout === "flipGrid";

    // 开始退出动画而不是立即恢复位置
    this.animateExitFocusMode(wasGroupedLayout, wasGridLayout);
  }

  /**
   * 平滑退出聚焦模式的动画
   */
  animateExitFocusMode(wasGroupedLayout, wasGridLayout) {
    // 检查聚焦对象是否存在，防止null引用错误
    if (!this.focusedObject) {
      console.warn("⚠️ animateExitFocusMode: focusedObject is null, falling back to immediate exit");
      this.immediateExitFocusMode(wasGroupedLayout, wasGridLayout);
      return;
    }

    // 确定动画持续时间，与进入动画保持一致
    const duration = this.currentLayout === "grouped" || this.currentLayout === "grouped4" ? 700 : 600;
    const startTime = Date.now();

    // 保存聚焦对象的当前状态
    const focusedObject = this.focusedObject;

    // 额外的安全检查：确保focusedObject有position和scale属性
    if (!focusedObject.position || !focusedObject.scale || !focusedObject.rotation) {
      console.warn("⚠️ animateExitFocusMode: focusedObject missing required properties, falling back to immediate exit");
      this.immediateExitFocusMode(wasGroupedLayout, wasGridLayout);
      return;
    }

    const startPos = {
      x: focusedObject.position.x,
      y: focusedObject.position.y,
      z: focusedObject.position.z,
    };
    const startScale = {
      x: focusedObject.scale.x,
      y: focusedObject.scale.y,
      z: focusedObject.scale.z,
    };
    const startRotation = {
      x: focusedObject.rotation.x,
      y: focusedObject.rotation.y,
      z: focusedObject.rotation.z,
    };

    // 找到聚焦对象的原始位置
    const focusedObjectIndex = this.objects.indexOf(focusedObject);
    const targetPos = this.originalPositions[focusedObjectIndex];

    if (!targetPos) {
      // 如果没有原始位置，立即退出
      console.warn("⚠️ animateExitFocusMode: No original position found, falling back to immediate exit");
      this.immediateExitFocusMode(wasGroupedLayout, wasGridLayout);
      return;
    }

    // 开始动画前设置状态
    this.isFocusMode = false;
    // 注意：保持 animationsPaused = true 直到动画完成，特别是对于网格布局

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用缓动函数
      const easeProgress = this.easeInOutCubic(progress);

      // 对于网格模式（PHOTOS1）、翻转网格模式和分组模式（PHOTOS2/PHOTOS4），需要动画垂直位置回到原始位置和缩放
      if (this.currentLayout === "grid" || this.currentLayout === "flipGrid" || this.currentLayout === "grouped" || this.currentLayout === "grouped4") {
        // 动画垂直位置回到原始位置（水平位置保持不变）
        focusedObject.position.y = startPos.y + (targetPos.y - startPos.y) * easeProgress;
        // 动画缩放回到原始大小
        const scaleProgress = startScale.x + (targetPos.scale.x - startScale.x) * easeProgress;
        focusedObject.scale.set(scaleProgress, scaleProgress, scaleProgress);
      } else {
        // 其他模式：动画聚焦对象回到原始位置
        focusedObject.position.x = startPos.x + (targetPos.x - startPos.x) * easeProgress;
        focusedObject.position.y = startPos.y + (targetPos.y - startPos.y) * easeProgress;
        focusedObject.position.z = startPos.z + (targetPos.z - startPos.z) * easeProgress;

        // 动画缩放回到原始大小
        const scaleProgress = startScale.x + (targetPos.scale.x - startScale.x) * easeProgress;
        focusedObject.scale.set(scaleProgress, scaleProgress, scaleProgress);
      }

      // 动画旋转回到原始状态（如果有的话）
      focusedObject.rotation.x = startRotation.x + (0 - startRotation.x) * easeProgress;
      focusedObject.rotation.y = startRotation.y + (0 - startRotation.y) * easeProgress;
      focusedObject.rotation.z = startRotation.z + (0 - startRotation.z) * easeProgress;

      this.renderCallback();

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        // 动画完成，完成退出聚焦模式的清理工作
        this.completeFocusExit(wasGroupedLayout, wasGridLayout);
      }
    };

    // 立即开始淡入其他元素的动画（与聚焦对象的退出动画同步）
    this.animateFadeInOtherElements();

    animate();
  }

  /**
   * 立即退出聚焦模式（无动画，用作后备方案）
   */
  immediateExitFocusMode(wasGroupedLayout, wasGridLayout) {
    this.isFocusMode = false;
    this.isExitingFocus = false; // 重置退出标志
    this.isFocusAnimationComplete = false; // 重置动画完成标志
    this.pendingFocusExit = false; // 清除待处理的退出请求
    // 注意：不立即设置 animationsPaused = false，让 completeFocusExit 处理

    // 移除所有与聚焦相关的CSS类并恢复元素样式
    for (let i = 0; i < this.objects.length; i++) {
      const element = this.objects[i].element;
      if (element) {
        this.removeFocusClasses(element);
        this.restoreElementStyles(element);
      }
    }

    // 移除header元素的聚焦相关CSS类
    if (wasGroupedLayout) {
      // PHOTOS2 mode: remove focus classes from category title2 headers
      for (let i = 0; i < this.categoryTitle2Objects.length; i++) {
        const titleObj = this.categoryTitle2Objects[i];
        const titleElement = titleObj.element;
        if (titleElement) {
          this.removeFocusClasses(titleElement);
          this.restoreElementStyles(titleElement);
        }
      }
    }

    // PHOTOS4 mode: remove focus classes from category title headers
    for (let i = 0; i < this.categoryTitleObjects.length; i++) {
      const titleObj = this.categoryTitleObjects[i];
      const titleElement = titleObj.element;
      if (titleElement) {
        this.removeFocusClasses(titleElement);
        this.restoreElementStyles(titleElement);
      }
    }

    // 恢复所有对象的原始位置和缩放
    for (let i = 0; i < this.objects.length; i++) {
      const obj = this.objects[i];
      const originalPos = this.originalPositions[i];

      if (originalPos) {
        obj.position.set(originalPos.x, originalPos.y, originalPos.z);
        obj.scale.set(originalPos.scale.x, originalPos.scale.y, originalPos.scale.z);
      }
    }

    this.completeFocusExit(wasGroupedLayout, wasGridLayout);
  }

  /**
   * 动画淡入其他元素（从10%透明度回到100%）
   */
  animateFadeInOtherElements() {
    console.log("🎭 animateFadeInOtherElements: Starting fade-in animation for non-focused elements", {
      currentLayout: this.currentLayout,
      objectCount: this.objects.length,
      focusedObject: this.focusedObject ? "exists" : "null",
    });

    // 移除聚焦对象的所有focus-related类（包括模板特定的类）
    if (this.focusedObject && this.focusedObject.element) {
      this.removeFocusClasses(this.focusedObject.element);
    }

    // 移除其他元素的所有focus-related类，让CSS过渡处理淡入效果
    for (let i = 0; i < this.objects.length; i++) {
      const obj = this.objects[i];
      const element = obj.element;

      if (obj !== this.focusedObject && element) {
        this.removeFocusClasses(element);
        // 确保元素恢复到正常状态
        this.restoreElementStyles(element);
      }
    }

    // 移除header元素的focus-fade-out类，让CSS过渡处理淡入效果
    if (this.currentLayout === "grouped") {
      // PHOTOS2 mode: remove focus-fade-out from category title2 headers
      for (let i = 0; i < this.categoryTitle2Objects.length; i++) {
        const titleObj = this.categoryTitle2Objects[i];
        const titleElement = titleObj.element;
        if (titleElement) {
          this.removeFocusClasses(titleElement);
        }
      }
    } else if (this.currentLayout === "grouped4") {
      // PHOTOS4 mode: remove focus-fade-out from category title headers
      for (let i = 0; i < this.categoryTitleObjects.length; i++) {
        const titleObj = this.categoryTitleObjects[i];
        const titleElement = titleObj.element;
        if (titleElement) {
          this.removeFocusClasses(titleElement);
        }
      }
    }

    // For FLIP GRID layout, add a small delay to ensure CSS transitions complete properly
    // This helps prevent race conditions when exit is triggered immediately after entry completion
    if (this.currentLayout === "flipGrid") {
      setTimeout(() => {
        console.log("🎭 animateFadeInOtherElements: FLIP GRID delayed cleanup completed");
        // Additional cleanup for FLIP GRID if needed
        this.renderCallback();
      }, 50); // Small delay to ensure CSS transitions have time to start
    }
  }

  /**
   * 移除元素上的所有focus-related CSS类（包括模板特定的类）
   */
  removeFocusClasses(element) {
    if (!element) return;

    // 移除所有可能的focus-related类
    const focusClasses = ["focus-fade-out", "focus-centered"];

    focusClasses.forEach((className) => {
      element.classList.remove(className);
    });
  }

  /**
   * 恢复元素的正常样式状态
   */
  restoreElementStyles(element) {
    if (!element) return;

    // 确保元素恢复到正常的透明度和cursor状态
    // 这些样式会被CSS过渡处理，但我们确保没有内联样式干扰
    element.style.removeProperty("opacity");
    element.style.removeProperty("cursor");
    element.style.removeProperty("z-index");
  }

  /**
   * 完成聚焦退出的清理工作
   */
  completeFocusExit(wasGroupedLayout, wasGridLayout) {
    console.log("🏁 completeFocusExit: Finalizing focus exit", {
      focusedObject: this.focusedObject ? "exists" : "null",
      wasGroupedLayout,
      wasGridLayout,
    });

    // 确保聚焦对象精确回到原始位置（如果存在）
    if (this.focusedObject) {
      const focusedObjectIndex = this.objects.indexOf(this.focusedObject);
      const originalPos = this.originalPositions[focusedObjectIndex];

      if (originalPos) {
        this.focusedObject.position.set(originalPos.x, originalPos.y, originalPos.z);
        this.focusedObject.scale.set(originalPos.scale.x, originalPos.scale.y, originalPos.scale.z);
        this.focusedObject.rotation.set(0, 0, 0);
      }
    }

    // 恢复所有其他对象到原始位置（如果它们被移动了）
    for (let i = 0; i < this.objects.length; i++) {
      const obj = this.objects[i];
      const originalPos = this.originalPositions[i];

      if (originalPos && obj !== this.focusedObject) {
        obj.position.set(originalPos.x, originalPos.y, originalPos.z);
        obj.scale.set(originalPos.scale.x, originalPos.scale.y, originalPos.scale.z);
      }
    }

    // 确保所有元素的CSS类和样式都被正确清理
    for (let i = 0; i < this.objects.length; i++) {
      const element = this.objects[i].element;
      if (element) {
        this.removeFocusClasses(element);
        this.restoreElementStyles(element);
      }
    }

    // 清理header元素的CSS类和样式
    if (wasGroupedLayout) {
      // PHOTOS2 mode: clean up category title2 headers
      for (let i = 0; i < this.categoryTitle2Objects.length; i++) {
        const titleObj = this.categoryTitle2Objects[i];
        const titleElement = titleObj.element;
        if (titleElement) {
          this.removeFocusClasses(titleElement);
          this.restoreElementStyles(titleElement);
        }
      }
    }

    // PHOTOS4 mode: clean up category title headers
    for (let i = 0; i < this.categoryTitleObjects.length; i++) {
      const titleObj = this.categoryTitleObjects[i];
      const titleElement = titleObj.element;
      if (titleElement) {
        this.removeFocusClasses(titleElement);
        this.restoreElementStyles(titleElement);
      }
    }

    // 现在动画完成，可以安全地恢复所有动画（包括网格滚动）
    this.animationsPaused = false;

    // 清理聚焦状态
    this.focusedObject = null;
    this.isExitingFocus = false; // 重置退出标志
    this.isFocusAnimationComplete = false; // 重置动画完成标志
    this.pendingFocusExit = false; // 清除待处理的退出请求
    this.originalPositions = [];
    this.renderCallback();

    // 在不同布局模式下，恢复相应的动画状态
    if (wasGroupedLayout) {
      // PHOTOS2和PHOTOS4模式的恢复逻辑（如果需要）
    } else if (wasGridLayout) {
      // 网格模式（PHOTOS1）和翻转网格模式的恢复逻辑：确保滚动动画平滑恢复
      if (this.currentLayout === "grid") {
        this.resumeGridScrollingAfterFocus();
      } else if (this.currentLayout === "flipGrid") {
        this.resumeFlipGridScrollingAfterFocus();
      }
    }

    // 调用聚焦模式退出回调
    if (this.focusCallbacks.onExitFocus) {
      this.focusCallbacks.onExitFocus(wasGroupedLayout || wasGridLayout);
    }
  }

  /**
   * 在聚焦退出后恢复网格滚动动画
   */
  resumeGridScrollingAfterFocus() {
    // 确保网格滚动动画数据与当前对象位置同步
    if (this.gridScrollAnimationData && this.gridScrollAnimationData.length > 0) {
      for (let i = 0; i < this.objects.length && i < this.gridScrollAnimationData.length; i++) {
        const obj = this.objects[i];
        const scrollData = this.gridScrollAnimationData[i];

        if (scrollData) {
          // 同步当前X位置到滚动动画数据，确保平滑继续
          scrollData.currentX = obj.position.x;

          // 重新计算环绕边界，以防在聚焦期间布局发生了变化
          const viewportWidth = window.innerWidth;
          const extendedViewport = viewportWidth * 1.5; // 扩展视口以提供缓冲
          scrollData.wrapRightX = extendedViewport;
          scrollData.wrapLeftX = -extendedViewport;
        }
      }

      // 立即检查视口状态以确保滚动动画正确恢复
      this.checkViewportAndUpdateScrolling();
    }
  }

  /**
   * 在聚焦退出后恢复翻转网格滚动动画
   */
  resumeFlipGridScrollingAfterFocus() {
    // 确保翻转网格滚动动画数据与当前对象位置同步
    if (this.flipGridScrollAnimationData && this.flipGridScrollAnimationData.length > 0) {
      for (let i = 0; i < this.objects.length && i < this.flipGridScrollAnimationData.length; i++) {
        const obj = this.objects[i];
        const scrollData = this.flipGridScrollAnimationData[i];

        if (scrollData) {
          // 同步当前Y位置到滚动动画数据，确保平滑继续
          scrollData.currentY = obj.position.y;

          // 重新计算环绕边界，以防在聚焦期间布局发生了变化
          const viewportHeight = window.innerHeight;
          const extendedViewport = viewportHeight * 1.5; // 扩展视口以提供缓冲
          scrollData.wrapTopY = extendedViewport;
          scrollData.wrapBottomY = -extendedViewport;
        }
      }

      // 立即检查视口状态以确保滚动动画正确恢复
      this.checkViewportAndUpdateScrolling();
    }
  }

  /**
   * 将聚焦对象动画化到中心位置
   */
  animateToFocusPosition(focusedObject) {
    const duration = this.currentLayout === "grouped" || this.currentLayout === "grouped4" ? 700 : 600; // PHOTOS2和PHOTOS4模式稍慢一些
    const startTime = Date.now();
    const startPos = {
      x: focusedObject.position.x,
      y: focusedObject.position.y,
      z: focusedObject.position.z,
    };

    // 保存起始旋转状态
    const startRotation = {
      x: focusedObject.rotation.x,
      y: focusedObject.rotation.y,
      z: focusedObject.rotation.z,
    };

    // 根据布局类型调整目标位置和缩放
    let targetPos, targetScale, targetRotation;

    if (this.currentLayout === "grid") {
      // 网格模式：水平位置保持不变，垂直方向居中，根据模式调整缩放
      targetPos = {
        x: startPos.x, // 保持原始水平位置
        y: 0, // 垂直方向居中
        z: startPos.z, // 保持原始深度位置
      };
      // PHOTOS5模式使用更大的缩放，PHOTOS1和PHOTOS3使用增强的缩放
      if (this.isPhotos5Mode) {
        targetScale = 2.2; // PHOTOS5: 2.2倍
      } else if (this.isPhotos1Mode) {
        targetScale = 3.0; // PHOTOS1: 3.0倍（大幅增强缩放）
      } else {
        targetScale = 1.6; // PHOTOS3和其他: 1.6倍
      }
      targetRotation = { x: 0, y: 0, z: 0 }; // 重置所有旋转
    } else if (this.currentLayout === "flipGrid") {
      // 翻转网格模式：水平位置保持不变（保持在列中），垂直方向居中，使用与网格模式相同的缩放
      targetPos = {
        x: startPos.x, // 保持原始水平位置（保持在列中）
        y: 0, // 垂直方向居中
        z: startPos.z, // 保持原始深度位置
      };
      // 使用与网格模式相同的缩放逻辑
      if (this.isPhotos5Mode) {
        targetScale = 2.2; // PHOTOS5: 2.2倍
      } else if (this.isPhotos1Mode) {
        targetScale = 3.0; // PHOTOS1: 3.0倍（大幅增强缩放）
      } else {
        targetScale = 1.6; // PHOTOS3和其他: 1.6倍
      }
      targetRotation = { x: 0, y: 0, z: 0 }; // 重置所有旋转
    } else if (this.currentLayout === "grouped" || this.currentLayout === "grouped4") {
      // PHOTOS2和PHOTOS4模式：保持水平位置，垂直居中，更大的缩放
      targetPos = {
        x: startPos.x, // 保持原始水平位置
        y: 0, // 垂直方向居中
        z: startPos.z, // 保持原始深度位置
      };
      targetScale = 2.6; // 更大的缩放倍数，从1.4增加到2.6
      targetRotation = { x: 0, y: 0, z: 0 }; // 重置所有旋转
    } else if (this.currentLayout === "random2") {
      // 瀑布雨模式：确保聚焦元素正面显示，与网格模式保持一致的垂直位置
      targetPos = {
        x: 0, // 水平中心
        y: 121, // 与网格模式一致的垂直位置
        z: 800, // 与随机模式相同的位置
      };
      targetScale = 1.35; // 稍大于随机模式的缩放
      targetRotation = {
        x: 0, // 保持x轴旋转为0
        y: 0, // 保持y轴旋转为0，确保正面显示
        z: 0, // 保持z轴旋转为0
      };
    } else {
      // 随机模式：与网格模式保持一致的垂直位置
      targetPos = {
        x: 0, // 水平中心
        y: 121, // 与网格模式一致的垂直位置
        z: 800, // 原有位置
      };
      targetScale = 1.3; // 1.3倍缩放
      targetRotation = { x: 0, y: 0, z: 0 }; // 重置所有旋转
    }

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用缓动函数
      const easeProgress = this.easeInOutCubic(progress);

      // 插值当前位置
      focusedObject.position.x = startPos.x + (targetPos.x - startPos.x) * easeProgress;
      focusedObject.position.y = startPos.y + (targetPos.y - startPos.y) * easeProgress;
      focusedObject.position.z = startPos.z + (targetPos.z - startPos.z) * easeProgress;

      // 根据布局类型应用不同的缩放
      const scale = 1 + (targetScale - 1) * easeProgress;
      focusedObject.scale.set(scale, scale, scale);

      // 根据布局类型应用不同的旋转处理
      focusedObject.rotation.x = startRotation.x + (targetRotation.x - startRotation.x) * easeProgress;
      focusedObject.rotation.y = startRotation.y + (targetRotation.y - startRotation.y) * easeProgress;
      focusedObject.rotation.z = startRotation.z + (targetRotation.z - startRotation.z) * easeProgress;

      this.renderCallback();

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        // 确保聚焦对象保持在最终位置和旋转
        focusedObject.position.set(targetPos.x, targetPos.y, targetPos.z);
        focusedObject.scale.set(targetScale, targetScale, targetScale);
        focusedObject.rotation.set(targetRotation.x, targetRotation.y, targetRotation.z);
        this.renderCallback();

        // 标记聚焦进入动画完成
        this.isFocusAnimationComplete = true;
        console.log("✅ Focus entry animation completed for layout:", this.currentLayout);

        // 检查是否有待处理的退出请求
        if (this.pendingFocusExit) {
          console.log("🔄 Executing pending focus exit request");
          this.pendingFocusExit = false;
          this.exitFocusMode();
        }
      }
    };

    animate();
  }

  /**
   * PERFORMANCE: Dynamic will-change property management for optimal layer creation
   */
  updateWillChangeProperty(layoutName) {
    for (let i = 0; i < this.objects.length; i++) {
      const element = this.objects[i].element;
      if (element) {
        if (layoutName === "random" || layoutName === "random2") {
          // Promote to composite layer during random and random2 animations
          element.style.willChange = "transform";
        } else {
          // Remove will-change for other layouts to reduce GPU memory usage
          element.style.willChange = "auto";
        }
      }
    }
  }

  /**
   * 基于当前布局更新动画
   */
  updateAnimations() {
    // 如果当前是随机布局，应用浮动动画效果
    if (this.currentLayout === "random") {
      this.updateRandomAnimation();
    }

    // 如果当前是瀑布雨布局，应用瀑布雨动画效果
    if (this.currentLayout === "random2") {
      this.updateRandom2Animation();
    }

    // 如果当前是网格布局，应用网格滚动动画效果
    if (this.currentLayout === "grid") {
      this.updateGridScrollAnimation();
    }

    // 如果当前是翻转网格布局，应用翻转网格垂直滚动动画效果
    if (this.currentLayout === "flipGrid") {
      this.updateFlipGridScrollAnimation();
    }

    // 如果当前是分组布局，应用分组布局动画（包括飞入动画和滚动动画）
    if (this.currentLayout === "grouped") {
      this.updateGroupedLayout();
    }

    // 如果当前是PHOTOS4分组布局，应用相同的分组布局动画
    if (this.currentLayout === "grouped4") {
      this.updateGroupedLayout();
    }
  }

  /**
   * 切换到指定布局
   */
  switchToLayout(layoutName, enableTransition = true) {
    const previousLayout = this.currentLayout;
    this.currentLayout = layoutName;

    // Clear category title labels when switching away from PHOTOS4 mode
    if (previousLayout === "grouped4" && layoutName !== "grouped4") {
      this.clearCategoryTitleLabels();
    }

    // PERFORMANCE: Manage will-change property dynamically
    this.updateWillChangeProperty(layoutName);

    // 记录动画开始时间
    this.animationStartTime = Date.now();

    if (layoutName === "grid") {
      // 从随机布局切换到网格布局时使用平滑过渡
      const useTransition = previousLayout === "random" && enableTransition;

      if (useTransition) {
        // 首先应用布局切换类以进行平滑过渡
        this.applyLayoutClasses("switching");
        // 带过渡的变换，然后应用最终布局类
        this.transform(this.targets.grid, useTransition);
        // 过渡开始后应用最终网格布局类
        setTimeout(() => this.applyLayoutClasses("grid"), 100);
      } else {
        // 无过渡的直接切换
        this.applyLayoutClasses("grid");
        this.transform(this.targets.grid, useTransition);
      }

      // 立即检查视口状态以决定是否启用滚动动画
      setTimeout(
        () => {
          this.checkViewportAndUpdateScrolling();
        },
        useTransition ? 900 : 100
      ); // 等待过渡动画完成后再检查
    } else if (layoutName === "flipGrid") {
      // 从其他布局切换到翻转网格布局时使用平滑过渡
      const useTransition = (previousLayout === "random" || previousLayout === "grid") && enableTransition;

      if (useTransition) {
        // 首先应用布局切换类以进行平滑过渡
        this.applyLayoutClasses("switching");
        // 带过渡的变换，然后应用最终布局类
        this.transform(this.targets.flipGrid, useTransition);
        // 过渡开始后应用最终翻转网格布局类
        setTimeout(() => this.applyLayoutClasses("flipGrid"), 100);
      } else {
        // 无过渡的直接切换
        this.applyLayoutClasses("flipGrid");
        this.transform(this.targets.flipGrid, useTransition);
      }

      // 立即检查视口状态以决定是否启用滚动动画
      setTimeout(
        () => {
          this.checkViewportAndUpdateScrolling();
        },
        useTransition ? 900 : 100
      ); // 等待过渡动画完成后再检查
    } else if (layoutName === "random") {
      // Apply smooth transition when switching to RANDOM layout
      const useTransition = enableTransition && (previousLayout === "grid" || previousLayout === "grouped");

      if (useTransition) {
        // Apply switching class for smooth transition
        this.applyLayoutClasses("switching");
        this.generateRandomLayout(); // Generate new random positions
        this.transform(this.targets.random, true); // FIXED: Enable smooth transitions for layout switching
        // Apply final random layout class after transition starts
        setTimeout(() => this.applyLayoutClasses("random"), 100);
      } else {
        // Direct switch without transition
        this.applyLayoutClasses("random");
        this.generateRandomLayout(); // 每次点击时重新生成随机布局
        this.transform(this.targets.random, true); // FIXED: Enable smooth transitions even for direct switch
      }

      this.lastRandomUpdate = Date.now(); // 初始化随机更新时间
    } else if (layoutName === "random2") {
      // Apply smooth transition when switching to waterfall rain layout
      const useTransition = enableTransition && (previousLayout === "grid" || previousLayout === "grouped" || previousLayout === "random");

      if (useTransition) {
        // Apply switching class for smooth transition
        this.applyLayoutClasses("switching");
        this.generateRandom2Layout(); // Generate new waterfall rain positions
        this.transform(this.targets.random2, true); // Enable smooth transitions for layout switching
        // Apply final waterfall rain layout class after transition starts
        setTimeout(() => this.applyLayoutClasses("random2"), 100);
      } else {
        // Direct switch without transition
        this.applyLayoutClasses("random2");
        this.generateRandom2Layout(); // 每次点击时重新生成瀑布雨布局
        this.transform(this.targets.random2, true); // Enable smooth transitions even for direct switch
      }

      this.lastRandom2Update = Date.now(); // 初始化瀑布雨更新时间
    } else if (layoutName === "grouped") {
      // 清除任何随机动画数据以防止干扰
      this.randomAnimationData = [];
      this.random2AnimationData = [];

      // 应用分组布局CSS类
      this.applyLayoutClasses("grouped");

      // Create category title2 labels for PHOTOS2 mode
      if (this.categoryTitleLabel2Template && this.lastGroupData) {
        this.createCategoryTitle2Labels(this.lastGroupData, this.lastGlobalTopY, this.lastRowHeight);
      }

      // 启动飞入动画
      if (enableTransition) {
        this.isGroupedFlyInActive = true;
        this.groupedFlyInStartTime = Date.now();
        this.setupGroupedFlyInAnimation();
      } else {
        // 直接切换到目标位置（无动画）
        this.transform(this.targets.grouped, false);
      }
    } else if (layoutName === "grouped4") {
      // PHOTOS4模式：使用与PHOTOS2完全相同的行为
      // 清除任何随机动画数据以防止干扰
      this.randomAnimationData = [];

      // 应用分组布局CSS类（使用grouped4类）
      this.applyLayoutClasses("grouped4");

      // Create category title labels for PHOTOS4 mode
      if (this.categoryTitleLabelTemplate && this.lastGroupData) {
        this.createCategoryTitleLabels(this.lastGroupData, this.lastGlobalTopY, this.lastRowHeight);
      }

      // 启动飞入动画（使用与PHOTOS2相同的逻辑）
      if (enableTransition) {
        this.isGrouped4FlyInActive = true;
        this.grouped4FlyInStartTime = Date.now();
        this.setupGrouped4FlyInAnimation();
      } else {
        // 直接切换到目标位置（无动画）
        this.transform(this.targets.grouped, false);
      }
    }
  }

  /**
   * 处理窗口调整大小事件
   */
  onWindowResize() {
    // 当窗口大小改变时，立即重新检查视口状态（主要视口更新触发器）
    if (this.currentLayout === "grid" || this.currentLayout === "flipGrid") {
      this.checkViewportAndUpdateScrolling();
    } else if (this.currentLayout === "grouped" || this.currentLayout === "grouped4") {
      this.checkGroupedViewportAndUpdateScrolling();
    }
  }

  /**
   * ENHANCED ANIMATIONS: Initialize pre-computed animation lookup tables for 60 FPS performance
   */
  initializeAnimationLookupTables() {
    console.log("Initializing animation lookup tables...");
    const startTime = Date.now();

    // Pre-compute sine and cosine values for smooth animations
    this.precomputedSines = [];
    this.precomputedCosines = [];

    for (let i = 0; i < this.animationLookupSize; i++) {
      const angle = (i / this.animationLookupSize) * Math.PI * 2; // Full circle
      this.precomputedSines.push(Math.sin(angle));
      this.precomputedCosines.push(Math.cos(angle));
    }

    const endTime = Date.now();
    console.log(`Animation lookup tables initialized: ${this.animationLookupSize} values in ${endTime - startTime}ms`);
  }

  /**
   * 设置键盘事件监听器
   */
  setupKeyboardListeners() {
    // 绑定键盘事件处理器
    this.keyDownHandler = (event) => this.handleKeyDown(event);
    document.addEventListener("keydown", this.keyDownHandler);

    // 绑定全局点击事件处理器以支持点击外部区域退出聚焦模式
    this.documentClickHandler = (event) => this.handleDocumentClick(event);
    document.addEventListener("click", this.documentClickHandler);
  }

  /**
   * 处理键盘事件
   */
  handleKeyDown(event) {
    // ESC键退出聚焦模式
    if (event.key === "Escape" && this.isFocusMode) {
      console.log("⌨️ ESC key pressed in focus mode", {
        currentLayout: this.currentLayout,
        animationComplete: this.isFocusAnimationComplete,
        isPhotos2Mode: this.isPhotos2Mode,
        isPhotos4Mode: this.isPhotos4Mode,
      });

      // PHOTOS2 mode: Prevent ESC key from exiting focus
      if (this.isPhotos2Mode && this.currentLayout === "grouped") {
        console.log("🔒 PHOTOS2 Sticky Focus: ESC key ignored, use return button to exit focus");
        return;
      }

      // PHOTOS4 mode: Prevent ESC key from exiting focus
      if (this.isPhotos4Mode && this.currentLayout === "grouped4") {
        console.log("🔒 PHOTOS4 Sticky Focus: ESC key ignored, use return button to exit focus");
        return;
      }

      // For other modes: allow ESC to exit focus (will be queued if animation not complete)
      this.exitFocusMode();
    }
  }

  /**
   * 处理全局点击事件 - 支持点击外部区域退出聚焦模式
   */
  handleDocumentClick(event) {
    // 只在聚焦模式下处理
    if (!this.isFocusMode) {
      return;
    }

    // 调试日志：记录点击事件和当前状态
    console.log("🖱️ handleDocumentClick: Focus mode active", {
      focusedObject: this.focusedObject ? "exists" : "null",
      currentLayout: this.currentLayout,
      isPhotos1Mode: this.isPhotos1Mode,
      isPhotos2Mode: this.isPhotos2Mode,
      isPhotos4Mode: this.isPhotos4Mode,
      isFocusAnimationComplete: this.isFocusAnimationComplete,
      pendingFocusExit: this.pendingFocusExit,
      clickTarget: event.target.tagName + (event.target.className ? "." + event.target.className : ""),
    });

    // PHOTOS1 mode: Enable click-outside-to-exit behavior for both grid and flipGrid layouts
    if (this.isPhotos1Mode && (this.currentLayout === "grid" || this.currentLayout === "flipGrid")) {
      // Check if the click is on any photo element
      let clickedOnPhoto = false;
      for (let i = 0; i < this.objects.length; i++) {
        const obj = this.objects[i];
        if (obj.element && obj.element.contains(event.target)) {
          clickedOnPhoto = true;
          break;
        }
      }

      // Check if the click is on any UI controls (navigation buttons, menus, etc.)
      const isUIControl =
        event.target.closest(".focus-nav-btn") ||
        event.target.closest(".gallery-controls") ||
        event.target.closest(".button-container") ||
        event.target.closest(".sub-button-container") ||
        event.target.closest(".menu") ||
        event.target.closest(".sub-menu-container") ||
        event.target.closest(".sub-menu-nav") ||
        event.target.closest(".sub-menu-content") ||
        event.target.closest(".headSelect") ||
        event.target.closest(".headSelect1") ||
        event.target.closest(".layout-btn") ||
        event.target.closest(".sub-nav-btn") ||
        event.target.closest(".grade-mode-btn") ||
        event.target.closest(".theme-mode-btn") ||
        event.target.closest(".swipe-hint");

      // If click is outside photos and UI controls, exit focus mode
      if (!clickedOnPhoto && !isUIControl) {
        console.log(`🎯 PHOTOS1 Mode: Click outside detected in ${this.currentLayout} layout, exiting focus mode`);
        this.exitFocusMode();
        return;
      }
    }

    // PHOTOS2 mode: Prevent focus loss from document clicks
    if (this.isPhotos2Mode && this.currentLayout === "grouped") {
      // In PHOTOS2 mode, only the return button should be able to exit focus
      // Check if the click is on the return button specifically
      const isReturnButton = event.target.closest(".focus-nav-btn.return-btn");

      if (!isReturnButton) {
        // Ignore all other clicks - maintain sticky focus
        console.log("🔒 PHOTOS2 Sticky Focus: Document click ignored, use return button to exit focus");
        return;
      }
    }

    // PHOTOS4 mode: Prevent focus loss from document clicks
    if (this.isPhotos4Mode && this.currentLayout === "grouped4") {
      // In PHOTOS4 mode, only the return button should be able to exit focus
      // Check if the click is on the return button specifically
      const isReturnButton = event.target.closest(".focus-nav-btn.return-btn");

      if (!isReturnButton) {
        // Ignore all other clicks - maintain sticky focus
        console.log("🔒 PHOTOS4 Sticky Focus: Document click ignored, use return button to exit focus");
        return;
      }
      // If it is the return button, let the button's own event handler manage the exit
      // The button handler calls e.stopPropagation(), so this should not normally execute
      // But if for some reason the event bubbles up, we handle it as a fallback
      console.log("🔓 PHOTOS4 Sticky Focus: Return button click detected in document handler (fallback)");

      // Add a small delay to avoid potential race conditions with the button's own handler
      setTimeout(() => {
        if (this.isFocusMode) {
          console.log("🔓 PHOTOS4 Sticky Focus: Executing fallback exit focus mode");
          this.exitFocusMode();
        }
      }, 10);
      return;
    }

    // For other modes (PHOTOS3, PHOTOS5, random, random2, flipGrid): original behavior
    // Note: PHOTOS1 mode is handled above with specific click-outside-to-exit logic
    // Note: PHOTOS2 and PHOTOS4 modes are handled above with sticky focus logic
    if (!this.isPhotos1Mode && !this.isPhotos2Mode && !this.isPhotos4Mode) {
      // RANDOM和FLIP GRID模式特殊处理：点击任何非聚焦元素都退出聚焦模式
      if (this.currentLayout === "random" || this.currentLayout === "flipGrid") {
        // 检查点击是否在聚焦元素上
        let clickedOnFocusedPhoto = false;
        if (this.focusedObject && this.focusedObject.element && this.focusedObject.element.contains(event.target)) {
          clickedOnFocusedPhoto = true;
        }

        // 检查点击是否在聚焦导航按钮上（避免意外退出）
        const isNavigationButton = event.target.closest(".focus-nav-btn");

        // 如果点击不在聚焦元素上且不在导航按钮上，退出聚焦模式
        if (!clickedOnFocusedPhoto && !isNavigationButton) {
          console.log(`🎯 ${this.currentLayout.toUpperCase()} Mode: Click on non-focused element detected, exiting focus mode`);
          this.exitFocusMode();
        }
      } else {
        // 其他模式的原始行为
        // 检查点击是否在任何照片元素上
        let clickedOnPhoto = false;
        for (let i = 0; i < this.objects.length; i++) {
          const obj = this.objects[i];
          if (obj.element && obj.element.contains(event.target)) {
            clickedOnPhoto = true;
            break;
          }
        }

        // 检查点击是否在聚焦导航按钮上（避免意外退出）
        const isNavigationButton = event.target.closest(".focus-nav-btn");

        // 如果点击不在任何照片元素上且不在导航按钮上，退出聚焦模式
        if (!clickedOnPhoto && !isNavigationButton) {
          this.exitFocusMode();
        }
      }
    }
  }

  /**
   * 获取当前布局名称
   */
  getCurrentLayout() {
    return this.currentLayout;
  }

  /**
   * 获取聚焦动画状态（用于调试）
   */
  getFocusAnimationState() {
    return {
      isFocusMode: this.isFocusMode,
      isFocusAnimationComplete: this.isFocusAnimationComplete,
      isExitingFocus: this.isExitingFocus,
      pendingFocusExit: this.pendingFocusExit,
      focusedObject: this.focusedObject ? "exists" : "null",
      currentLayout: this.currentLayout,
    };
  }

  /**
   * 获取布局目标
   */
  getTargets() {
    return this.targets;
  }

  /**
   * 获取分组布局列滚动的当前统一速度
   */
  getGroupedColumnScrollSpeed() {
    return this.groupedColumnScrollSpeed;
  }

  /**
   * 检查是否处于聚焦模式
   */
  isInFocusMode() {
    return this.isFocusMode;
  }

  /**
   * 清理方法
   */
  destroy() {
    // Clear category title labels
    this.clearCategoryTitleLabels();
    // Clear category title2 labels
    this.clearCategoryTitle2Labels();

    // 清除动画数据
    this.randomAnimationData = [];
    this.gridScrollAnimationData = [];
    this.flipGridScrollAnimationData = [];
    this.groupedScrollAnimationData = [];
    this.groupedFlyInAnimationData = [];
    this.grouped4FlyInAnimationData = [];
    this.gridFlyInAnimationData = [];
    this.originalPositions = [];

    // ENHANCED ANIMATIONS: Clean up animation lookup tables
    this.precomputedSines = [];
    this.precomputedCosines = [];

    // 重置状态
    this.focusedObject = null;
    this.isFocusMode = false;
    this.isExitingFocus = false;
    this.isFocusAnimationComplete = false;
    this.pendingFocusExit = false;
    this.animationsPaused = false;
    this.isGroupedFlyInActive = false;
    this.isGrouped4FlyInActive = false;
    this.isGridFlyInActive = false;

    // 清理键盘事件监听器
    if (this.keyDownHandler) {
      document.removeEventListener("keydown", this.keyDownHandler);
      this.keyDownHandler = null;
    }

    // 清理全局点击事件监听器
    if (this.documentClickHandler) {
      document.removeEventListener("click", this.documentClickHandler);
      this.documentClickHandler = null;
    }
  }
}
