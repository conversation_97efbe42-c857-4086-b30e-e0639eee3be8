<template>
  <!-- Category Title Label Component for PHOTOS2 Mode - Hidden by default, used for cloning -->
  <div id="category-title-label2-template" class="category-title-label2" style="display: none" ref="templateRef">
    <div class="category-title-text2">三年级</div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineExpose } from "vue";

// Template reference for external access
const templateRef = ref(null);

// Props for component configuration
const props = defineProps({
  // Allow customization of template ID if needed
  templateId: {
    type: String,
    default: "category-title-label2-template",
  },
  // Allow visibility control from parent
  visible: {
    type: Boolean,
    default: false,
  },
});

// Update template ID and visibility when component mounts
onMounted(() => {
  if (templateRef.value) {
    // Set custom template ID if provided
    if (props.templateId !== "category-title-label2-template") {
      templateRef.value.id = props.templateId;
    }

    // Update visibility based on props
    if (props.visible) {
      templateRef.value.style.display = "";
    }
  } else {
    console.warn("⚠️ Template reference not available in CategoryTitleLabel2");
  }
});

// Expose template reference and utility methods for parent access
defineExpose({
  templateRef,
  getTemplateElement: () => templateRef.value,
  setVisible: (visible) => {
    if (templateRef.value) {
      templateRef.value.style.display = visible ? "" : "none";
    }
  },
  getId: () => templateRef.value?.id || props.templateId,
});
</script>

<style scoped>
/* ===== CATEGORY TITLE LABEL STYLES (PHOTOS2 MODE) ===== */
.category-title-label2 {
  position: absolute;
  width: auto;
  height: auto;
  width: 482px;
  height: 110px;
  border-radius: 12px;
  cursor: default;
  backdrop-filter: blur(8px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* Add containment for child elements */
  contain: layout style;
  /* Optimize for 3D transforms - enables hardware acceleration */
  will-change: transform;
  /* Enhanced hardware acceleration for smooth rightward movement animations */
  transform-style: preserve-3d;
  backface-visibility: hidden;
  padding: 14px 20px;
  text-align: center;
  z-index: 10;
  background-image: url("../../public/q.png");
  background-repeat: no-repeat;
  background-size: contain;

  display: flex;
  justify-content: center;
  align-items: center;
}

/* Title text for category label - PHOTOS2 specific styling */
.category-title-text2 {
  font-size: 18px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 6px;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 0.3px;
  text-transform: capitalize;

  color: black !important;

  font-size: 40px;

  width: 36%;
  height: 100%;
  margin-right: 25px;

  display: flex;
  justify-content: center;
  align-items: center;

  letter-spacing: 3px;
}

/* Decorative underline for category label - PHOTOS2 specific styling */
.category-title-underline2 {
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, rgba(80, 120, 200, 0.7) 0%, rgba(60, 100, 180, 0.7) 50%, rgba(80, 120, 200, 0.7) 100%);
  border-radius: 1px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  transition: all 0.3s ease;
}

.category-title-label2:hover .category-title-underline2 {
  background: linear-gradient(90deg, rgba(100, 140, 220, 1) 0%, rgba(80, 120, 200, 1) 50%, rgba(100, 140, 220, 1) 100%);
  box-shadow: 0 2px 5px rgba(80, 120, 200, 0.4);
  transform: scaleX(1.03);
}

/* Category-specific title colors for PHOTOS2 mode */
.category-title-label2[data-category="wildlife"] .category-title-text2 {
  color: rgba(255, 193, 7, 0.95); /* Golden yellow for wildlife */
}

.category-title-label2[data-category="wildlife"] .category-title-underline2 {
  background: linear-gradient(90deg, rgba(255, 193, 7, 0.7) 0%, rgba(255, 152, 0, 0.7) 50%, rgba(255, 193, 7, 0.7) 100%);
}

.category-title-label2[data-category="cuisine"] .category-title-text2 {
  color: rgba(255, 87, 34, 0.95); /* Orange for cuisine */
}

.category-title-label2[data-category="cuisine"] .category-title-underline2 {
  background: linear-gradient(90deg, rgba(255, 87, 34, 0.7) 0%, rgba(255, 69, 0, 0.7) 50%, rgba(255, 87, 34, 0.7) 100%);
}

.category-title-label2[data-category="sports"] .category-title-text2 {
  color: rgba(76, 175, 80, 0.95); /* Green for sports */
}

.category-title-label2[data-category="sports"] .category-title-underline2 {
  background: linear-gradient(90deg, rgba(76, 175, 80, 0.7) 0%, rgba(56, 142, 60, 0.7) 50%, rgba(76, 175, 80, 0.7) 100%);
}

/* Focus mode styles for category title label */
.category-title-label2.focus-fade-out {
  opacity: 0.1 !important;
  transition: opacity 0.4s ease !important;
}

.category-title-label2.focus-centered {
  opacity: 1 !important;
  z-index: 1000;
  transition: opacity 0.4s ease !important;
  box-shadow: 0 15px 50px rgba(80, 120, 200, 0.4) !important;
  border-color: rgba(80, 120, 200, 0.8) !important;
}

/* Instant positioning for category title label */
.category-title-label2.instant-position {
  transition: none !important;
}

.category-title-label2.instant-position * {
  transition: none !important;
}

/* Fly-in animation for category title label */
.category-title-label2.fly-in-active {
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .category-title-label2 {
    min-width: 150px;
    max-width: 400px;
    padding: 12px 16px;
  }

  .category-title-text2 {
    font-size: 16px;
  }
}
</style>
