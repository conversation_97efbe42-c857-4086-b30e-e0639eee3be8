<template>
  <div class="App" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd">
    <div class="SceneCon" id="SceneCon"></div>

    <!-- Swipe hint indicator -->
    <div class="swipe-hint" :class="{ hidden: !swipeHintVisible || isHeadSelectVisible }">
      <div class="swipe-hint-icon">⬇</div>
      <div class="swipe-hint-text">向下滑动</div>
    </div>

    <div class="headSelect" :class="{ visible: isHeadSelectVisible }" @click="hideHeadSelect">
      <!-- First headSelect1 div with screensaver button -->

      <div class="headSelect1" id="thememode" @click="activateThemeMode" v-show="buttonVisibility.allButtonsVisible && buttonVisibility.topRightControls.groupVisible">
        <div class="icon1"></div>
        <button class="theme-mode-btn" :class="{ active: isThemeModeActive }" v-show="buttonVisibility.topRightControls.themeMode">主题模式</button>
      </div>
      <!-- Second headSelect1 div with grade mode button -->
      <div class="headSelect1" id="grademode" @click="activateGradeMode" v-show="buttonVisibility.allButtonsVisible && buttonVisibility.topRightControls.groupVisible">
        <div class="icon2"></div>
        <button class="grade-mode-btn" :class="{ active: isGradeModeActive }" v-show="buttonVisibility.topRightControls.gradeMode">年级模式</button>
      </div>
      <div class="headSelect1" id="" v-show="buttonVisibility.allButtonsVisible && buttonVisibility.topRightControls.groupVisible">
        <div class="icon3"></div>
        <button class="grade-mode-btn" :class="{ active: isGradeModeActive }" v-show="buttonVisibility.topRightControls.gradeMode">活动模式</button>
      </div>

      <div class="headSelect1" id="screensaver" @click="toggleScreensaverMode" v-show="buttonVisibility.allButtonsVisible && buttonVisibility.topRightControls.groupVisible">
        <div class="icon4"></div>
        <button
          class="screensaver-btn"
          :class="{
            active: isScreensaverActive,
            countdown: screensaverCountdown > 0,
          }"
          @click="toggleScreensaverMode"
          v-show="buttonVisibility.topRightControls.screensaver"
        >
          <span v-if="!isScreensaverActive">屏保模式</span>
          <span v-else-if="screensaverCountdown > 0">
            {{ screensaverCountdown }}s -
            {{ screensaverCycleState.isOnRandomStep ? "RANDOM" : screensaverCycleState.isOnFlipGridStep ? "FLIPGRID" : `PHOTOS1-${screensaverCycleState.currentPhotos1Index + 1}` }}
          </span>
          <span v-else>屏保循环中...</span>
        </button>
      </div>

      <!-- Third headSelect1 div with theme mode button -->
    </div>

    <!-- Swiper Gallery Modal Component -->
    <SwiperGallery
      :is-visible="isSwiperMode"
      :photo-dataset="swiperImgs"
      @slide-click="handleCarouselSlideClick"
      @carousel-ready="handleCarouselReady"
      @carousel-error="handleCarouselError"
      @close-modal="closeSwiperModal"
      ref="swiperGalleryRef"
    />

    <!-- Main Layout Buttons Menu -->
    <div class="menu" id="menu" v-show="buttonVisibility.allButtonsVisible && buttonVisibility.layoutButtons.groupVisible">
      <button id="grid" class="layout-btn" v-show="buttonVisibility.layoutButtons.grid">GRID</button>
      <button id="flipGrid" class="layout-btn flipgrid-btn" v-show="buttonVisibility.layoutButtons.flipGrid">FLIP GRID</button>
      <button id="random" class="layout-btn" v-show="buttonVisibility.layoutButtons.random">RANDOM</button>
      <button id="random2" class="layout-btn random2-btn" v-show="buttonVisibility.layoutButtons.random2">RAIN</button>
      <button id="swiper" class="layout-btn" v-show="buttonVisibility.layoutButtons.swiper">SWIPER</button>
      <button id="photos5" class="layout-btn" v-show="buttonVisibility.layoutButtons.photos5">PHOTOS 5</button>
      <button id="photos4" class="layout-btn" v-show="buttonVisibility.layoutButtons.photos4">PHOTOS 4</button>
      <button id="photos3" class="layout-btn" v-show="buttonVisibility.layoutButtons.photos3">PHOTOS 3</button>
      <button id="photos2" class="layout-btn" v-show="buttonVisibility.layoutButtons.photos2">PHOTOS 2</button>
      <button id="photos1" class="layout-btn" v-show="buttonVisibility.layoutButtons.photos1">PHOTOS 1</button>
    </div>

    <!-- Sub-buttons Container -->
    <div class="sub-menu-container" v-show="buttonVisibility.allButtonsVisible && buttonVisibility.layoutButtons.groupVisible">
      <!-- Navigation buttons for sub-menu -->
      <div class="sub-menu-nav" v-show="activeSubButton.type !== null">
        <button class="sub-nav-btn sub-nav-prev" @click="navigateSubButton('prev')" :disabled="!canNavigateSubButton('prev')" title="Previous sub-button">←</button>
        <div class="sub-nav-indicator">
          {{ activeSubButton.index + 1 }} /
          {{ getSubButtonCount(activeSubButton.type) }}
        </div>
        <button class="sub-nav-btn sub-nav-next" @click="navigateSubButton('next')" :disabled="!canNavigateSubButton('next')" title="Next sub-button">→</button>
      </div>

      <div class="sub-menu-content">
        <!-- PHOTOS1 Sub-buttons - dynamically generated based on photos array length -->
        <button
          v-for="(subsetInfo, index) in photos1Subsets"
          :key="`photos1-sub-${index}`"
          :id="`photos1-sub-${index + 1}`"
          :class="[
            'layout-btn',
            'photos1-sub-btn',
            'sub-btn',
            {
              'active-sub-button': activeSubButton.type === 'photos1' && activeSubButton.index === index,
            },
          ]"
          v-show="buttonVisibility.layoutButtons.photos1Subs && buttonVisibility.layoutButtons.photos1Subs[index]"
          @click="handlePhotos1SubButtonClick(index)"
          :title="`Photos ${subsetInfo.start + 1}-${subsetInfo.end} (${subsetInfo.count} photos)`"
        >
          PHOTOS1-{{ index + 1 }} ({{ subsetInfo.start + 1 }}-{{ subsetInfo.end }})
        </button>

        <!-- PHOTOS2 Sub-buttons - dynamically generated based on category groups -->
        <button
          v-for="(subsetInfo, index) in photos2Subsets"
          :key="`photos2-sub-${index}`"
          :id="`photos2-sub-${index + 1}`"
          :class="[
            'layout-btn',
            'photos2-sub-btn',
            'sub-btn',
            {
              'active-sub-button': activeSubButton.type === 'photos2' && activeSubButton.index === index,
            },
          ]"
          v-show="buttonVisibility.layoutButtons.photos2Subs && buttonVisibility.layoutButtons.photos2Subs[index]"
          @click="handlePhotos2SubButtonClick(index)"
          :title="`Categories: ${subsetInfo.categoryNames} (${subsetInfo.totalPhotos} photos total)`"
        >
          PHOTOS2-{{ index + 1 }} ({{ subsetInfo.categoryNames }})
        </button>

        <!-- PHOTOS3 Sub-buttons - dynamically generated based on photo count -->
        <button
          v-for="(subsetInfo, index) in photos3Subsets"
          :key="`photos3-sub-${index}`"
          :id="`photos3-sub-${index + 1}`"
          :class="[
            'layout-btn',
            'photos3-sub-btn',
            'sub-btn',
            {
              'active-sub-button': activeSubButton.type === 'photos3' && activeSubButton.index === index,
            },
          ]"
          v-show="buttonVisibility.layoutButtons.photos3Subs && buttonVisibility.layoutButtons.photos3Subs[index]"
          @click="handlePhotos3SubButtonClick(index)"
          :title="`Photos ${subsetInfo.start + 1}-${subsetInfo.end} (${subsetInfo.count} photos)`"
        >
          PHOTOS3-{{ index + 1 }} ({{ subsetInfo.start + 1 }}-{{ subsetInfo.end }})
        </button>

        <!-- PHOTOS4 Sub-buttons - dynamically generated based on category groups -->
        <button
          v-for="(subsetInfo, index) in photos4Subsets"
          :key="`photos4-sub-${index}`"
          :id="`photos4-sub-${index + 1}`"
          :class="[
            'layout-btn',
            'photos4-sub-btn',
            'sub-btn',
            {
              'active-sub-button': activeSubButton.type === 'photos4' && activeSubButton.index === index,
            },
          ]"
          v-show="buttonVisibility.layoutButtons.photos4Subs && buttonVisibility.layoutButtons.photos4Subs[index]"
          @click="handlePhotos4SubButtonClick(index)"
          :title="`Categories: ${subsetInfo.categoryNames} (${subsetInfo.totalPhotos} photos total)`"
        >
          PHOTOS4-{{ index + 1 }} ({{ subsetInfo.categoryNames }})
        </button>

        <!-- PHOTOS5 Sub-buttons - dynamically generated based on photo count -->
        <button
          v-for="(subsetInfo, index) in photos5Subsets"
          :key="`photos5-sub-${index}`"
          :id="`photos5-sub-${index + 1}`"
          :class="[
            'layout-btn',
            'photos5-sub-btn',
            'sub-btn',
            {
              'active-sub-button': activeSubButton.type === 'photos5' && activeSubButton.index === index,
            },
          ]"
          v-show="buttonVisibility.layoutButtons.photos5Subs && buttonVisibility.layoutButtons.photos5Subs[index]"
          @click="handlePhotos5SubButtonClick(index)"
          :title="`Photos ${subsetInfo.start + 1}-${subsetInfo.end} (${subsetInfo.count} photos)`"
        >
          PHOTOS5-{{ index + 1 }} ({{ subsetInfo.start + 1 }}-{{ subsetInfo.end }})
        </button>
      </div>
    </div>

    <!-- Debug info (remove in production) -->
    <div
      v-if="false"
      style="position: fixed; top: 100px; left: 10px; background: rgba(0, 0, 0, 0.8); color: white; padding: 10px; font-size: 12px; z-index: 9999; max-width: 400px"
    >
      <div><strong>PHOTOS1 Subsets:</strong></div>
      <div>Total photos: {{ photos.length }}</div>
      <div>Subsets created: {{ photos1Subsets.length }}</div>
      <div v-for="(subset, i) in photos1Subsets" :key="i">
        PHOTOS1-{{ i + 1 }}: {{ subset.start + 1 }}-{{ subset.end }} ({{ subset.count }}
        photos)
      </div>

      <div style="margin-top: 10px">
        <strong>PHOTOS2 Category Subsets:</strong>
      </div>
      <div>Total categories: {{ photos2.length }}</div>
      <div>Category subsets: {{ photos2Subsets.length }}</div>
      <div v-for="(subset, i) in photos2Subsets" :key="i">
        PHOTOS2-{{ i + 1 }}: {{ subset.categoryNames }} ({{ subset.totalPhotos }}
        photos)
      </div>

      <div style="margin-top: 10px"><strong>PHOTOS3 Subsets:</strong></div>
      <div>Total photos: {{ photos3.length }}</div>
      <div>Subsets created: {{ photos3Subsets.length }}</div>
      <div v-for="(subset, i) in photos3Subsets" :key="i">
        PHOTOS3-{{ i + 1 }}: {{ subset.start + 1 }}-{{ subset.end }} ({{ subset.count }}
        photos)
      </div>

      <div style="margin-top: 10px">
        <strong>PHOTOS4 Category Subsets:</strong>
      </div>
      <div>Total categories: {{ photos4.length }}</div>
      <div>Category subsets: {{ photos4Subsets.length }}</div>
      <div v-for="(subset, i) in photos4Subsets" :key="i">
        PHOTOS4-{{ i + 1 }}: {{ subset.categoryNames }} ({{ subset.totalPhotos }}
        photos)
      </div>

      <div style="margin-top: 10px"><strong>PHOTOS5 Subsets:</strong></div>
      <div>Total photos: {{ photos5.length }}</div>
      <div>Subsets created: {{ photos5Subsets.length }}</div>
      <div v-for="(subset, i) in photos5Subsets" :key="i">
        PHOTOS5-{{ i + 1 }}: {{ subset.start + 1 }}-{{ subset.end }} ({{ subset.count }}
        photos)
      </div>
    </div>

    <!-- Standard Template Component -->
    <PhotoFrameStandardTemplate ref="standardTemplateRef" />

    <!-- PHOTOS 1 Template Component -->
    <PhotoFramePhotos1Template ref="photos1TemplateRef" />

    <!-- PHOTOS 3 Template Component -->
    <PhotoFramePhotos3Template ref="photos3TemplateRef" />

    <!-- Grouped Template Component -->
    <PhotoFrameGroupedTemplate ref="groupedTemplateRef" />

    <!-- Grouped4 Template Component (PHOTOS4 Mode) -->
    <PhotoFrameGrouped4Template ref="grouped4TemplateRef" />

    <!-- Category Title Label Component (for PHOTOS4 Mode) -->
    <CategoryTitleLabel ref="categoryTitleLabelRef" />

    <!-- Category Title Label Component (for PHOTOS2 Mode) -->
    <CategoryTitleLabel2 ref="categoryTitleLabel2Ref" />

    <!-- PHOTOS 5 Template Component -->
    <PhotoFramePhotos5Template ref="photos5TemplateRef" />
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, watch, computed, watchEffect } from "vue";
import * as THREE from "three";
import { CSS3DRenderer, CSS3DObject } from "three/examples/jsm/renderers/CSS3DRenderer.js";
import { LayoutManager } from "./layouts/LayoutManager.js";
import SwiperGallery from "./components/SwiperGallery.vue";
import PhotoFramePhotos1Template from "./components/PhotoFramePhotos1Template.vue";
import PhotoFramePhotos3Template from "./components/PhotoFramePhotos3Template.vue";
import PhotoFramePhotos5Template from "./components/PhotoFramePhotos5Template.vue";
import PhotoFrameStandardTemplate from "./components/PhotoFrameStandardTemplate.vue";
import PhotoFrameGroupedTemplate from "./components/PhotoFrameGroupedTemplate.vue";
import PhotoFrameGrouped4Template from "./components/PhotoFrameGrouped4Template.vue";
import CategoryTitleLabel from "./components/CategoryTitleLabel.vue";
import CategoryTitleLabel2 from "./components/CategoryTitleLabel2.vue";
import { get3dEffect, getStudentCount, getStudentList, getStudentArtworkList, likeArtwork } from "./api/index.js";
/*
屏保模型：PHOTOS 1，RANDOM，FLIPGRID
年级模式：photos2，photos3，SWIPER
主题模式:photos4,photos5
*/
// Sample photo data - replace with your own images (will be replaced by API data)
let photos = ref([
  {
    id: 1,
    src: "https://picsum.photos/1500/1700?random=1",
    title: "Mountain Landscape",
    description: "Beautiful mountain view",
  },
  {
    id: 2,
    src: "https://picsum.photos/1500/1700?random=2",
    title: "Ocean Sunset",
    description: "Peaceful ocean scene",
  },
  {
    id: 3,
    src: "https://picsum.photos/1500/1700?random=3",
    title: "Forest Path",
    description: "Mysterious forest trail",
  },
  {
    id: 4,
    src: "https://picsum.photos/1500/1700?random=4",
    title: "City Lights",
    description: "Urban nightscape",
  },
  {
    id: 5,
    src: "https://picsum.photos/1500/1700?random=5",
    title: "Desert Dunes",
    description: "Golden sand dunes",
  },
  {
    id: 6,
    src: "https://picsum.photos/1500/1700?random=6",
    title: "Snowy Peaks",
    description: "Snow-capped mountains",
  },
  {
    id: 7,
    src: "https://picsum.photos/1500/1700?random=7",
    title: "Tropical Beach",
    description: "Paradise beach view",
  },
  {
    id: 8,
    src: "https://picsum.photos/1500/1700?random=8",
    title: "Autumn Forest",
    description: "Colorful fall foliage",
  },
  {
    id: 9,
    src: "https://picsum.photos/1500/1700?random=9",
    title: "River Valley",
    description: "Serene river landscape",
  },
  {
    id: 10,
    src: "https://picsum.photos/1500/1700?random=10",
    title: "Northern Lights",
    description: "Aurora borealis display",
  },
  {
    id: 11,
    src: "https://picsum.photos/1500/1700?random=11",
    title: "Canyon View",
    description: "Grand canyon vista",
  },
  {
    id: 12,
    src: "https://picsum.photos/1500/1700?random=12",
    title: "Flower Field",
    description: "Blooming wildflowers",
  },
  {
    id: 13,
    src: "https://picsum.photos/1500/1700?random=13",
    title: "Lake Reflection",
    description: "Mirror-like lake",
  },
  {
    id: 14,
    src: "https://picsum.photos/1500/1700?random=14",
    title: "Starry Night",
    description: "Milky way galaxy",
  },
  {
    id: 15,
    src: "https://picsum.photos/1500/1700?random=15",
    title: "Waterfall",
    description: "Cascading waterfall",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=16",
    title: "Prairie Sunset",
    description: "Golden hour on plains",
  },
]);

const selectStudentId = ref(null);
// Expand initial photos array for demo purposes
while (photos.value.length < 30) {
  photos.value = photos.value.concat(photos.value);
}

// Create photo subsets for PHOTOS1 sub-buttons (100 photos per subset)
const createPhotoSubsets = (photoArray, subsetSize = 100) => {
  const subsets = [];
  const subsetInfo = [];

  for (let i = 0; i < photoArray.length; i += subsetSize) {
    const subset = photoArray.slice(i, i + subsetSize);
    subsets.push(subset);
    subsetInfo.push({
      start: i,
      end: Math.min(i + subsetSize, photoArray.length),
      count: subset.length,
    });
  }

  return { subsets, subsetInfo };
};

// Reactive computed properties for PHOTOS1 subsets
const photos1SubsetData = computed(() => createPhotoSubsets(photos.value, 150));
const photos1SubsetArrays = computed(() => photos1SubsetData.value.subsets);
const photos1Subsets = computed(() => photos1SubsetData.value.subsetInfo);

// Log subset creation (will update reactively)

// Create category-based subsets for PHOTOS2 sub-buttons (complete category groups)
const createCategorySubsets = (categoryArray, categoriesPerSubset = 2) => {
  const subsets = [];
  const subsetInfo = [];

  for (let i = 0; i < categoryArray.length; i += categoriesPerSubset) {
    const categorySubset = categoryArray.slice(i, i + categoriesPerSubset);
    subsets.push(categorySubset);

    // Calculate total photos in this category subset
    const totalPhotos = categorySubset.reduce((total, category) => total + category.photos.length, 0);
    const categoryNames = categorySubset.map((cat) => cat.category).join(", ");

    subsetInfo.push({
      startIndex: i,
      endIndex: Math.min(i + categoriesPerSubset, categoryArray.length) - 1,
      categoryCount: categorySubset.length,
      totalPhotos: totalPhotos,
      categoryNames: categoryNames,
      categories: categorySubset.map((cat) => cat.category),
    });
  }

  return { subsets, subsetInfo };
};

// Second photo dataset with nested category groups for organized layout
const photos2 = ref([
  {
    category: "nature",
    title: "三年级",
    photos: [
      {
        id: 1,
        title: "5画植物主题支持十一字",
        description: "张凌萱",
      },
      {
        id: 2,
        title: "数学竞赛获奖作品",
        description: "李明华",
      },
      {
        id: 3,
        title: "科学实验报告",
        description: "王小红",
      },
      {
        id: 4,
        title: "美术创作展示",
        description: "陈小明",
      },
      {
        id: 5,
        title: "语文作文优秀",
        description: "刘小芳",
      },
      {
        id: 6,
        title: "英语口语表演",
        description: "赵小强",
      },
    ],
  },
  {
    category: "architecture",
    title: "四年级",
    photos: [
      {
        id: 7,
        title: "建筑设计模型制作",
        description: "孙小华",
      },
      {
        id: 8,
        title: "历史文化研究报告",
        description: "周小丽",
      },
      {
        id: 9,
        title: "手工艺品制作",
        description: "吴小军",
      },
      {
        id: 10,
        title: "音乐演奏表演",
        description: "郑小燕",
      },
      {
        id: 11,
        title: "体育运动技能",
        description: "马小东",
      },
    ],
  },
  {
    category: "abstract",
    title: "五年级",
    photos: [
      {
        id: 12,
        title: "创意绘画作品",
        description: "林小雨",
      },
      {
        id: 13,
        title: "几何图形设计",
        description: "黄小天",
      },
      {
        id: 14,
        title: "光影艺术创作",
        description: "许小月",
      },
      {
        id: 15,
        title: "材质纹理研究",
        description: "谢小阳",
      },
      {
        id: 16,
        title: "动态艺术表现",
        description: "袁小星",
      },
      {
        id: 17,
        title: "色彩搭配实验",
        description: "何小云",
      },
      {
        id: 18,
        title: "抽象思维训练",
        description: "韩小风",
      },
    ],
  },
  {
    category: "science",
    title: "六年级",
    photos: [
      {
        id: 19,
        title: "科学实验探索",
        description: "田小宇",
      },
      {
        id: 20,
        title: "数学逻辑推理",
        description: "宋小晨",
      },
      {
        id: 21,
        title: "物理现象观察",
        description: "江小波",
      },
      {
        id: 22,
        title: "化学反应记录",
        description: "范小雪",
      },
      {
        id: 23,
        title: "生物标本制作",
        description: "邓小龙",
      },
      {
        id: 24,
        title: "地理环境调研",
        description: "曾小梅",
      },
      {
        id: 25,
        title: "天文观测报告",
        description: "石小鹏",
      },
    ],
  },
  {
    category: "arts",
    title: "特长班",
    photos: [
      {
        id: 26,
        title: "书法练习作品",
        description: "冯小静",
      },
      {
        id: 27,
        title: "舞蹈表演视频",
        description: "高小亮",
      },
      {
        id: 28,
        title: "器乐演奏录音",
        description: "贺小娟",
      },
      {
        id: 29,
        title: "戏剧表演片段",
        description: "侯小刚",
      },
      {
        id: 30,
        title: "朗诵比赛作品",
        description: "姜小丽",
      },
      {
        id: 31,
        title: "摄影作品集",
        description: "康小伟",
      },
      {
        id: 32,
        title: "手工制作展示",
        description: "雷小敏",
      },
    ],
  },
]);

// Create PHOTOS2 category subsets after photos2 is defined - make reactive
const photos2SubsetData = computed(() => createCategorySubsets(photos2.value, 3));
const photos2SubsetArrays = computed(() => photos2SubsetData.value.subsets);
const photos2Subsets = computed(() => photos2SubsetData.value.subsetInfo);

// Log PHOTOS2 subset creation

// Third photo dataset with unique content for PHOTOS3 mode
const photos3 = ref([
  {
    id: 1,
    src: "https://picsum.photos/1500/1700?random=201",
    title: "1号",
    description: "李萱敏",
    xb: "男",
  },
  {
    id: 2,
    src: "https://picsum.photos/1500/1700?random=202",
    title: "1号",
    description: "李萱敏",
    xb: "男",
  },
  {
    id: 3,
    src: "https://picsum.photos/1500/1700?random=203",
    title: "1号",
    description: "李萱敏",
    xb: "男",
  },
  {
    id: 4,
    src: "https://picsum.photos/1500/1700?random=204",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
  {
    id: 5,
    src: "https://picsum.photos/1500/1700?random=205",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
  {
    id: 6,
    src: "https://picsum.photos/1500/1700?random=206",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
  {
    id: 7,
    src: "https://picsum.photos/1500/1700?random=207",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
  {
    id: 8,
    src: "https://picsum.photos/1500/1700?random=208",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
  {
    id: 9,
    src: "https://picsum.photos/1500/1700?random=209",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
  {
    id: 1,
    src: "https://picsum.photos/1500/1700?random=201",
    title: "1号",
    description: "李萱敏",
    xb: "男",
  },
  {
    id: 2,
    src: "https://picsum.photos/1500/1700?random=202",
    title: "1号",
    description: "李萱敏",
    xb: "男",
  },
  {
    id: 3,
    src: "https://picsum.photos/1500/1700?random=203",
    title: "1号",
    description: "李萱敏",
    xb: "男",
  },
  {
    id: 4,
    src: "https://picsum.photos/1500/1700?random=204",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
  {
    id: 5,
    src: "https://picsum.photos/1500/1700?random=205",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
  {
    id: 6,
    src: "https://picsum.photos/1500/1700?random=206",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
  {
    id: 7,
    src: "https://picsum.photos/1500/1700?random=207",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
  {
    id: 8,
    src: "https://picsum.photos/1500/1700?random=208",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
  {
    id: 9,
    src: "https://picsum.photos/1500/1700?random=209",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
  {
    id: 1,
    src: "https://picsum.photos/1500/1700?random=201",
    title: "1号",
    description: "李萱敏",
    xb: "男",
  },
  {
    id: 2,
    src: "https://picsum.photos/1500/1700?random=202",
    title: "1号",
    description: "李萱敏",
    xb: "男",
  },
  {
    id: 3,
    src: "https://picsum.photos/1500/1700?random=203",
    title: "1号",
    description: "李萱敏",
    xb: "男",
  },
  {
    id: 4,
    src: "https://picsum.photos/1500/1700?random=204",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
  {
    id: 5,
    src: "https://picsum.photos/1500/1700?random=205",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
  {
    id: 6,
    src: "https://picsum.photos/1500/1700?random=206",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
  {
    id: 7,
    src: "https://picsum.photos/1500/1700?random=207",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
  {
    id: 8,
    src: "https://picsum.photos/1500/1700?random=208",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
  {
    id: 9,
    src: "https://picsum.photos/1500/1700?random=209",
    title: "1号",
    description: "李萱敏",
    xb: "女",
  },
]);

// Reactive computed properties for PHOTOS3 subsets (using smaller subset size for better distribution)
const photos3SubsetData = computed(() => createPhotoSubsets(photos3.value, 12));
const photos3SubsetArrays = computed(() => photos3SubsetData.value.subsets);
const photos3Subsets = computed(() => photos3SubsetData.value.subsetInfo);

// Fourth photo dataset with diverse categories for PHOTOS4 mode (text-only, following PHOTOS2 pattern)
const photos4 = ref([
  {
    category: "wildlife",
    title: "321312312",
    photos: [
      {
        id: 1,
        title: "African Elephant",
        description: "Majestic elephant in savanna",
      },
      {
        id: 2,
        title: "Bengal Tiger",
        description: "Powerful tiger in jungle",
      },
      {
        id: 3,
        title: "Arctic Wolf",
        description: "White wolf in snow",
      },
      {
        id: 4,
        title: "Golden Eagle",
        description: "Soaring bird of prey",
      },
      {
        id: 5,
        title: "Polar Bear",
        description: "Arctic giant on ice",
      },
      {
        id: 6,
        title: "Red Panda",
        description: "Cute climbing mammal",
      },
    ],
  },
  {
    category: "space",
    title: "Space Exploration",
    photos: [
      {
        id: 7,
        title: "Nebula Formation",
        description: "Colorful cosmic clouds",
      },
      {
        id: 8,
        title: "Mars Surface",
        description: "Red planet landscape",
      },
      {
        id: 9,
        title: "Space Station",
        description: "International space station",
      },
      {
        id: 10,
        title: "Galaxy Spiral",
        description: "Distant spiral galaxy",
      },
      {
        id: 11,
        title: "Rocket Launch",
        description: "Space mission takeoff",
      },
      {
        id: 12,
        title: "Astronaut EVA",
        description: "Spacewalk activity",
      },
      {
        id: 13,
        title: "Moon Crater",
        description: "Lunar surface detail",
      },
    ],
  },
  {
    category: "cuisine",
    title: "World Cuisine",
    photos: [
      {
        id: 14,
        title: "Italian Pasta",
        description: "Fresh homemade pasta",
      },
      {
        id: 15,
        title: "Japanese Sushi",
        description: "Traditional sushi platter",
      },
      {
        id: 16,
        title: "French Pastry",
        description: "Delicate croissants",
      },
      {
        id: 17,
        title: "Indian Curry",
        description: "Spicy aromatic curry",
      },
      {
        id: 18,
        title: "Mexican Tacos",
        description: "Authentic street tacos",
      },
      {
        id: 19,
        title: "Thai Pad Thai",
        description: "Classic stir-fried noodles",
      },
    ],
  },
  {
    category: "sports",
    title: "Sports & Athletics",
    photos: [
      {
        id: 20,
        title: "Soccer Match",
        description: "World cup action",
      },
      {
        id: 21,
        title: "Basketball Dunk",
        description: "High-flying slam dunk",
      },
      {
        id: 22,
        title: "Tennis Serve",
        description: "Professional tennis serve",
      },
      {
        id: 23,
        title: "Swimming Race",
        description: "Olympic pool competition",
      },
      {
        id: 24,
        title: "Mountain Climbing",
        description: "Rock climbing adventure",
      },
      {
        id: 25,
        title: "Cycling Tour",
        description: "Professional bike race",
      },
      {
        id: 26,
        title: "Surfing Wave",
        description: "Perfect wave riding",
      },
      {
        id: 27,
        title: "Marathon Run",
        description: "Long distance running",
      },
    ],
  },
  {
    category: "sports",
    title: "Sports & Athletics",
    photos: [
      {
        id: 20,
        title: "Soccer Match",
        description: "World cup action",
      },
      {
        id: 21,
        title: "Basketball Dunk",
        description: "High-flying slam dunk",
      },
      {
        id: 22,
        title: "Tennis Serve",
        description: "Professional tennis serve",
      },
      {
        id: 23,
        title: "Swimming Race",
        description: "Olympic pool competition",
      },
      {
        id: 24,
        title: "Mountain Climbing",
        description: "Rock climbing adventure",
      },
      {
        id: 25,
        title: "Cycling Tour",
        description: "Professional bike race",
      },
      {
        id: 26,
        title: "Surfing Wave",
        description: "Perfect wave riding",
      },
      {
        id: 27,
        title: "Marathon Run",
        description: "Long distance running",
      },
    ],
  },
]);

// Create PHOTOS4 category subsets after photos4 is defined (using 2 categories per subset like PHOTOS2) - make reactive
const photos4SubsetData = computed(() => createCategorySubsets(photos4.value, 2));
const photos4SubsetArrays = computed(() => photos4SubsetData.value.subsets);
const photos4Subsets = computed(() => photos4SubsetData.value.subsetInfo);

// Fifth photo dataset with flat structure for PHOTOS5 mode (similar to PHOTOS3)
const photos5 = ref([
  {
    id: 1,
    src: "https://picsum.photos/1500/1700?random=401",
    title: "Ocean Depths",
    description: "Deep sea exploration",
  },
  {
    id: 2,
    src: "https://picsum.photos/1500/1700?random=402",
    title: "Mountain Peak",
    description: "Himalayan summit view",
  },
  {
    id: 3,
    src: "https://picsum.photos/1500/1700?random=403",
    title: "Desert Oasis",
    description: "Hidden water source",
  },
  {
    id: 4,
    src: "https://picsum.photos/1500/1700?random=404",
    title: "Forest Canopy",
    description: "Rainforest treetops",
  },
  {
    id: 5,
    src: "https://picsum.photos/1500/1700?random=405",
    title: "Arctic Tundra",
    description: "Frozen wilderness",
  },
  {
    id: 6,
    src: "https://picsum.photos/1500/1700?random=406",
    title: "Coral Reef",
    description: "Underwater paradise",
  },
  {
    id: 7,
    src: "https://picsum.photos/1500/1700?random=407",
    title: "Volcanic Crater",
    description: "Active volcano rim",
  },
  {
    id: 8,
    src: "https://picsum.photos/1500/1700?random=408",
    title: "Cave System",
    description: "Underground chambers",
  },
  {
    id: 9,
    src: "https://picsum.photos/1500/1700?random=409",
    title: "Glacier Field",
    description: "Ice age remnants",
  },
  {
    id: 10,
    src: "https://picsum.photos/1500/1700?random=410",
    title: "Prairie Storm",
    description: "Lightning over plains",
  },
  {
    id: 11,
    src: "https://picsum.photos/1500/1700?random=411",
    title: "Coastal Cliffs",
    description: "Dramatic shoreline",
  },
  {
    id: 12,
    src: "https://picsum.photos/1500/1700?random=412",
    title: "River Delta",
    description: "Water meets land",
  },
  {
    id: 13,
    src: "https://picsum.photos/1500/1700?random=413",
    title: "Sand Dunes",
    description: "Shifting desert sands",
  },
  {
    id: 14,
    src: "https://picsum.photos/1500/1700?random=414",
    title: "Hot Springs",
    description: "Geothermal wonders",
  },
  {
    id: 15,
    src: "https://picsum.photos/1500/1700?random=415",
    title: "Meteor Shower",
    description: "Celestial light show",
  },
  {
    id: 16,
    src: "https://picsum.photos/1500/1700?random=416",
    title: "Ancient Ruins",
    description: "Lost civilization",
  },
  {
    id: 17,
    src: "https://picsum.photos/1500/1700?random=417",
    title: "Bamboo Grove",
    description: "Zen garden path",
  },
  {
    id: 18,
    src: "https://picsum.photos/1500/1700?random=418",
    title: "Salt Flats",
    description: "Mirror of the sky",
  },
  {
    id: 19,
    src: "https://picsum.photos/1500/1700?random=419",
    title: "Fjord Valley",
    description: "Carved by glaciers",
  },
  {
    id: 20,
    src: "https://picsum.photos/1500/1700?random=420",
    title: "Butterfly Migration",
    description: "Nature's journey",
  },
  {
    id: 21,
    src: "https://picsum.photos/1500/1700?random=421",
    title: "Crystal Cave",
    description: "Underground gems",
  },
  {
    id: 22,
    src: "https://picsum.photos/1500/1700?random=422",
    title: "Sunset Mesa",
    description: "Golden hour plateau",
  },
  {
    id: 23,
    src: "https://picsum.photos/1500/1700?random=423",
    title: "Misty Valley",
    description: "Morning fog blanket",
  },
  {
    id: 24,
    src: "https://picsum.photos/1500/1700?random=424",
    title: "Starry Desert",
    description: "Night sky canvas",
  },
  {
    id: 25,
    src: "https://picsum.photos/1500/1700?random=425",
    title: "Waterfall Pool",
    description: "Natural swimming hole",
  },
  {
    id: 26,
    src: "https://picsum.photos/1500/1700?random=426",
    title: "Wind Erosion",
    description: "Sculpted rock formations",
  },
  {
    id: 27,
    src: "https://picsum.photos/1500/1700?random=427",
    title: "Thermal Vents",
    description: "Earth's breathing holes",
  },
  {
    id: 28,
    src: "https://picsum.photos/1500/1700?random=428",
    title: "Bioluminescence",
    description: "Living light display",
  },
  {
    id: 29,
    src: "https://picsum.photos/1500/1700?random=429",
    title: "Canyon Echo",
    description: "Sound and stone",
  },
  {
    id: 30,
    src: "https://picsum.photos/1500/1700?random=430",
    title: "Alpine Lake",
    description: "Mountain mirror",
  },
  {
    id: 1,
    src: "https://picsum.photos/1500/1700?random=401",
    title: "Ocean Depths",
    description: "Deep sea exploration",
  },
  {
    id: 2,
    src: "https://picsum.photos/1500/1700?random=402",
    title: "Mountain Peak",
    description: "Himalayan summit view",
  },
  {
    id: 3,
    src: "https://picsum.photos/1500/1700?random=403",
    title: "Desert Oasis",
    description: "Hidden water source",
  },
  {
    id: 4,
    src: "https://picsum.photos/1500/1700?random=404",
    title: "Forest Canopy",
    description: "Rainforest treetops",
  },
  {
    id: 5,
    src: "https://picsum.photos/1500/1700?random=405",
    title: "Arctic Tundra",
    description: "Frozen wilderness",
  },
  {
    id: 6,
    src: "https://picsum.photos/1500/1700?random=406",
    title: "Coral Reef",
    description: "Underwater paradise",
  },
  {
    id: 7,
    src: "https://picsum.photos/1500/1700?random=407",
    title: "Volcanic Crater",
    description: "Active volcano rim",
  },
  {
    id: 8,
    src: "https://picsum.photos/1500/1700?random=408",
    title: "Cave System",
    description: "Underground chambers",
  },
  {
    id: 9,
    src: "https://picsum.photos/1500/1700?random=409",
    title: "Glacier Field",
    description: "Ice age remnants",
  },
  {
    id: 10,
    src: "https://picsum.photos/1500/1700?random=410",
    title: "Prairie Storm",
    description: "Lightning over plains",
  },
  {
    id: 11,
    src: "https://picsum.photos/1500/1700?random=411",
    title: "Coastal Cliffs",
    description: "Dramatic shoreline",
  },
  {
    id: 12,
    src: "https://picsum.photos/1500/1700?random=412",
    title: "River Delta",
    description: "Water meets land",
  },
  {
    id: 13,
    src: "https://picsum.photos/1500/1700?random=413",
    title: "Sand Dunes",
    description: "Shifting desert sands",
  },
  {
    id: 14,
    src: "https://picsum.photos/1500/1700?random=414",
    title: "Hot Springs",
    description: "Geothermal wonders",
  },
  {
    id: 15,
    src: "https://picsum.photos/1500/1700?random=415",
    title: "Meteor Shower",
    description: "Celestial light show",
  },
  {
    id: 4,
    src: "https://picsum.photos/1500/1700?random=404",
    title: "Forest Canopy",
    description: "Rainforest treetops",
  },
  {
    id: 5,
    src: "https://picsum.photos/1500/1700?random=405",
    title: "Arctic Tundra",
    description: "Frozen wilderness",
  },
  {
    id: 6,
    src: "https://picsum.photos/1500/1700?random=406",
    title: "Coral Reef",
    description: "Underwater paradise",
  },
  {
    id: 7,
    src: "https://picsum.photos/1500/1700?random=407",
    title: "Volcanic Crater",
    description: "Active volcano rim",
  },
  {
    id: 8,
    src: "https://picsum.photos/1500/1700?random=408",
    title: "Cave System",
    description: "Underground chambers",
  },
  {
    id: 9,
    src: "https://picsum.photos/1500/1700?random=409",
    title: "Glacier Field",
    description: "Ice age remnants",
  },
  {
    id: 10,
    src: "https://picsum.photos/1500/1700?random=410",
    title: "Prairie Storm",
    description: "Lightning over plains",
  },
  {
    id: 11,
    src: "https://picsum.photos/1500/1700?random=411",
    title: "Coastal Cliffs",
    description: "Dramatic shoreline",
  },
  {
    id: 12,
    src: "https://picsum.photos/1500/1700?random=412",
    title: "River Delta",
    description: "Water meets land",
  },
  {
    id: 13,
    src: "https://picsum.photos/1500/1700?random=413",
    title: "Sand Dunes",
    description: "Shifting desert sands",
  },
  {
    id: 14,
    src: "https://picsum.photos/1500/1700?random=414",
    title: "Hot Springs",
    description: "Geothermal wonders",
  },
  {
    id: 15,
    src: "https://picsum.photos/1500/1700?random=415",
    title: "Meteor Shower",
    description: "Celestial light show",
  },
]);

// Reactive computed properties for PHOTOS5 subsets (using smaller subset size for better distribution like PHOTOS3)
const photos5SubsetData = computed(() => createPhotoSubsets(photos5.value, 10));
const photos5SubsetArrays = computed(() => photos5SubsetData.value.subsets);
const photos5Subsets = computed(() => photos5SubsetData.value.subsetInfo);

// Dedicated SWIPER photo dataset for Swiper gallery modal
const SWIPERImg = ref([
  {
    id: 1,
    src: "https://picsum.photos/1500/1700?random=501",
    title: "画出你最喜欢的植物",
    description: "五年级2班-张凌萱",
    zan: "55",
    ax: "21",
  },
  {
    id: 2,
    src: "https://picsum.photos/1500/1700?random=502",
    title: "画出你最喜欢的植物",
    description: "五年级2班-张凌萱",
    zan: "55",
    ax: "21",
  },
  {
    id: 3,
    src: "https://picsum.photos/1500/1700?random=503",
    title: "画出你最喜欢的植物",
    description: "五年级2班-张凌萱",
    zan: "55",
    ax: "21",
  },
  {
    id: 4,
    src: "https://picsum.photos/1500/1700?random=504",
    title: "画出你最喜欢的植物",
    description: "五年级2班-张凌萱",
    zan: "55",
    ax: "21",
  },
  {
    id: 5,
    src: "https://picsum.photos/1500/1700?random=505",
    title: "画出你最喜欢的植物",
    description: "五年级2班-张凌萱",
    zan: "55",
    ax: "21",
  },
  {
    id: 6,
    src: "https://picsum.photos/1500/1700?random=506",
    title: "画出你最喜欢的植物",
    description: "五年级2班-张凌萱",
    zan: "55",
    ax: "21",
  },
  {
    id: 7,
    src: "https://picsum.photos/1500/1700?random=507",
    title: "画出你最喜欢的植物",
    description: "五年级2班-张凌萱",
    zan: "55",
    ax: "21",
  },
  {
    id: 8,
    src: "https://picsum.photos/1500/1700?random=508",
    title: "画出你最喜欢的植物",
    description: "五年级2班-张凌萱",
    zan: "55",
    ax: "21",
  },
  {
    id: 9,
    src: "https://picsum.photos/1500/1700?random=509",
    title: "画出你最喜欢的植物",
    description: "五年级2班-张凌萱",
    zan: "55",
    ax: "21",
  },
  {
    id: 10,
    src: "https://picsum.photos/1500/1700?random=510",
    title: "画出你最喜欢的植物",
    description: "五年级2班-张凌萱",
    zan: "55",
    ax: "21",
  },
]);

// Vue reactive data
const isSwiperMode = ref(false);
const swiperGalleryRef = ref(null);

// Watch for swiper mode changes
watch(isSwiperMode, (newValue) => {
  // Swiper mode state changed
});
const galleryInstance = ref(null);

// Component references
const standardTemplateRef = ref(null);
const photos1TemplateRef = ref(null);
const photos3TemplateRef = ref(null);
const photos5TemplateRef = ref(null);
const groupedTemplateRef = ref(null);
const grouped4TemplateRef = ref(null);
const categoryTitleLabelRef = ref(null);
const categoryTitleLabel2Ref = ref(null);

// Screensaver mode reactive data
const isScreensaverActive = ref(false);
const screensaverCountdown = ref(0);
const screensaverTimer = ref(null);
const screensaverCountdownInterval = ref(null);

// Automated cycling sequence state
const screensaverCycleState = ref({
  currentPhotos1Index: 0, // Current PHOTOS1 sub-button index (0-based)
  isOnRandomStep: false, // Whether currently on RANDOM step
  isOnFlipGridStep: false, // Whether currently on FLIPGRID step
  cycleTimer: null, // Timer for the cycling sequence
});

// Grade mode reactive data
const isGradeModeActive = ref(false);

// Theme mode reactive data
const isThemeModeActive = ref(false);

// Focus mode navigation buttons reactive data
const isFocusModeActive = ref(false);
const focusedImageData = ref(null);

// Independent SWIPER button highlight state (separate from dataset-based highlighting)
const isSwiperButtonIndependentHighlight = ref(false);

// HeadSelect visibility and swipe gesture state
const isHeadSelectVisible = ref(false);
const swipeHintVisible = ref(true); // Show hint initially
const swipeGestureState = ref({
  startY: 0,
  startTime: 0,
  isTracking: false,
  minSwipeDistance: 50, // Minimum distance for a valid swipe
  maxSwipeTime: 500, // Maximum time for a valid swipe (ms)
  hideTimeout: null, // Auto-hide timeout
  autoHideDelay: 3000, // Auto-hide after 3 seconds
  hintTimeout: null, // Hint auto-hide timeout
  hintAutoHideDelay: 5000, // Hide hint after 5 seconds
});

// Active sub-button state tracking for navigation
const activeSubButton = ref({
  type: null, // 'photos1', 'photos2', 'photos3', 'photos4', 'photos5', or null
  index: -1, // Index within the specific sub-button type
});

// 全局当前模式状态 - 基于明确的模式标识而不是数据结构判断
const currentMode = ref({
  type: null, // 'photos1', 'photos2', 'photos3', 'photos4', 'photos5', 'grid', 'random', 'flipGrid', 'random2', 'swiper'
  templateType: null, // 'photos1', 'photos3', 'photos5', 'grouped', 'grouped4', 'standard'
  layoutType: null, // 'grid', 'grouped', 'grouped4', 'random', 'flipGrid', 'random2'
});

// Button visibility control reactive data
const buttonVisibility = ref({
  // Top-right controls group
  topRightControls: {
    screensaver: true,
    gradeMode: true,
    themeMode: true,
    groupVisible: true, // Master control for entire top-right group
  },
  // Bottom menu layout buttons group
  layoutButtons: {
    grid: true,
    flipGrid: true,
    random: true,
    random2: true,
    swiper: true,
    photos1: true,
    photos2: true,
    photos3: true,
    photos4: true,
    photos5: true,
    // PHOTOS1 sub-buttons visibility (dynamically created based on photo subsets)
    photos1Subs: computed(() => photos1SubsetArrays.value.map(() => false)), // Show all sub-buttons by default
    // PHOTOS2 sub-buttons visibility (dynamically created based on category subsets)
    photos2Subs: computed(() => photos2SubsetArrays.value.map(() => false)), // Show all sub-buttons by default
    // PHOTOS3 sub-buttons visibility (dynamically created based on photo subsets)
    photos3Subs: computed(() => photos3SubsetArrays.value.map(() => false)), // Show all sub-buttons by default
    // PHOTOS4 sub-buttons visibility (dynamically created based on category subsets)
    photos4Subs: computed(() => photos4SubsetArrays.value.map(() => false)), // Show all sub-buttons by default
    // PHOTOS5 sub-buttons visibility (dynamically created based on photo subsets)
    photos5Subs: computed(() => photos5SubsetArrays.value.map(() => false)), // Show all sub-buttons by default
    groupVisible: true, // Master control for entire layout buttons group
  },
  // Global master control
  allButtonsVisible: true,
});
const swiperImgs = ref([]);
// Computed property for current photo dataset
const currentPhotoDataset = computed(() => {
  // When in Swiper mode, use the dedicated SWIPERImg dataset
  if (isSwiperMode.value) {
    if (selectStudentId.value) {
      // get3dEffect({sid: 92, student_id: selectStudentId.value}).then((res) => {
      get3dEffect({ sid: 1, student_id: 3387 }).then((res) => {
        console.log("SWIPERImg", res.data.data);
        SWIPERImg.value = res.data.data.map((item) => {
          return {
            id: item.id,
            src: item.file,
            title: item.title,
            description: item.description,
            zan: item.like,
            ax: item.praise,
          };
        });
        console.log("SWIPERImg", SWIPERImg.value);
        // return SWIPERImg.value;
      });
    }
    return SWIPERImg.value;
  }

  // Otherwise, use the current gallery dataset
  if (galleryInstance.value) {
    return galleryInstance.value.currentFlatDataset || galleryInstance.value.currentDataset || photos.value;
  }
  console.log("photos", photos.value);
  return photos.value;
});

// Event handlers for Custom Carousel Gallery component
const handleCarouselSlideClick = (data) => {};

const handleCarouselReady = (carouselData) => {
  // Demo: Show independent SWIPER button highlighting functionality
  demoSwiperButtonIndependentHighlight();
};

const handleCarouselError = (error) => {};

const closeSwiperModal = () => {
  // Resume PHOTOS3 grid scrolling animations when closing Swiper modal
  if (
    galleryInstance.value &&
    galleryInstance.value.layoutManager &&
    galleryInstance.value.layoutManager.isPhotos3Mode &&
    galleryInstance.value.layoutManager.currentLayout === "grid"
  ) {
    galleryInstance.value.layoutManager.isFocusMode = false; // 恢复PHOTOS3网格滚动动画
  }

  isSwiperMode.value = false;

  // Clear independent SWIPER button highlight when modal closes
  setSwiperButtonIndependentHighlight(false);
};

// Independent SWIPER button highlight management methods
const setSwiperButtonIndependentHighlight = (highlight = true) => {
  isSwiperButtonIndependentHighlight.value = highlight;

  const swiperBtn = document.getElementById("swiper");
  if (swiperBtn) {
    if (highlight) {
      swiperBtn.classList.add("swiper-independent-highlight");
    } else {
      swiperBtn.classList.remove("swiper-independent-highlight");
    }
  }
};

const clearSwiperButtonIndependentHighlight = () => {
  setSwiperButtonIndependentHighlight(false);
};

// Demo function to showcase independent SWIPER button highlighting
const demoSwiperButtonIndependentHighlight = () => {
  // Demo scenario: Show that SWIPER button can be highlighted independently
  // even when different datasets are active
  setTimeout(() => {
    setSwiperButtonIndependentHighlight(true);
  }, 2000);

  setTimeout(() => {
    setSwiperButtonIndependentHighlight(false);
  }, 5000);
};

// HeadSelect visibility management methods
const showHeadSelect = () => {
  isHeadSelectVisible.value = true;

  // Hide the hint when headSelect is shown
  hideSwipeHint();

  // Clear any existing auto-hide timeout
  if (swipeGestureState.value.hideTimeout) {
    clearTimeout(swipeGestureState.value.hideTimeout);
  }

  // Set auto-hide timeout
  swipeGestureState.value.hideTimeout = setTimeout(() => {
    hideHeadSelect();
  }, swipeGestureState.value.autoHideDelay);
};

const hideHeadSelect = () => {
  isHeadSelectVisible.value = false;

  // Clear auto-hide timeout
  if (swipeGestureState.value.hideTimeout) {
    clearTimeout(swipeGestureState.value.hideTimeout);
    swipeGestureState.value.hideTimeout = null;
  }
};

// Swipe hint management methods
const hideSwipeHint = () => {
  swipeHintVisible.value = false;

  // Clear hint timeout
  if (swipeGestureState.value.hintTimeout) {
    clearTimeout(swipeGestureState.value.hintTimeout);
    swipeGestureState.value.hintTimeout = null;
  }
};

const startHintAutoHide = () => {
  // Auto-hide hint after delay
  swipeGestureState.value.hintTimeout = setTimeout(() => {
    hideSwipeHint();
  }, swipeGestureState.value.hintAutoHideDelay);
};

// Touch/Swipe gesture detection methods
const handleTouchStart = (event) => {
  const touch = event.touches[0];
  swipeGestureState.value.startY = touch.clientY;
  swipeGestureState.value.startTime = Date.now();
  swipeGestureState.value.isTracking = true;

  // Hide hint on first touch interaction
  if (swipeHintVisible.value) {
    hideSwipeHint();
  }
};

const handleTouchMove = (event) => {
  if (!swipeGestureState.value.isTracking) return;

  // Prevent default scrolling behavior during swipe detection
  const touch = event.touches[0];
  const currentY = touch.clientY;
  const deltaY = currentY - swipeGestureState.value.startY;

  // Only prevent default if this looks like a downward swipe from the top
  if (swipeGestureState.value.startY < 100 && deltaY > 0) {
    event.preventDefault();
  }
};

const handleTouchEnd = (event) => {
  if (!swipeGestureState.value.isTracking) return;

  const touch = event.changedTouches[0];
  const endY = touch.clientY;
  const endTime = Date.now();

  const deltaY = endY - swipeGestureState.value.startY;
  const deltaTime = endTime - swipeGestureState.value.startTime;

  // Reset tracking state
  swipeGestureState.value.isTracking = false;

  // Check if this is a valid downward swipe
  const isValidSwipe =
    deltaY > swipeGestureState.value.minSwipeDistance && // Sufficient distance
    deltaTime < swipeGestureState.value.maxSwipeTime && // Within time limit
    swipeGestureState.value.startY < 100; // Started from top area

  if (isValidSwipe && !isHeadSelectVisible.value) {
    showHeadSelect();
  }
};

// Screensaver mode functions
const toggleScreensaverMode = () => {
  if (isScreensaverActive.value) {
    // Stop screensaver mode
    stopScreensaverMode();
  } else {
    // Start screensaver mode
    startScreensaverMode();
  }
};

const startScreensaverMode = () => {
  // Set screensaver as active
  isScreensaverActive.value = true;
  screensaverCountdown.value = 20;

  // Initialize cycling state - start with PHOTOS1-1
  screensaverCycleState.value = {
    currentPhotos1Index: 0,
    isOnRandomStep: false, // Start with PHOTOS1 mode
    isOnFlipGridStep: false,
    cycleTimer: null,
  };

  // Immediately trigger PHOTOS1-1 (first sub-button)
  handlePhotos1SubButtonClick(0, true); // true = automated call

  // Start countdown timer
  screensaverCountdownInterval.value = setInterval(() => {
    screensaverCountdown.value--;

    if (screensaverCountdown.value <= 0) {
      // Execute next step in cycling sequence
      executeNextScreensaverStep();
    }
  }, 1000);
};

const executeNextScreensaverStep = () => {
  // Reset countdown for next cycle
  screensaverCountdown.value = 20;

  // Check if user is currently in focus mode - if so, pause screensaver cycling
  if (galleryInstance.value && galleryInstance.value.layoutManager && galleryInstance.value.layoutManager.isInFocusMode()) {
    // User is in focus mode, pause screensaver cycling until they exit focus mode
    return;
  }

  if (!screensaverCycleState.value.isOnRandomStep && !screensaverCycleState.value.isOnFlipGridStep) {
    // Currently on PHOTOS1 sub-button, switch to RANDOM
    screensaverCycleState.value.isOnRandomStep = true;

    // Trigger RANDOM button functionality
    if (galleryInstance.value) {
      galleryInstance.value.switchToCSS3DMode();
      galleryInstance.value.layoutManager.switchToLayout("random", true);
    }
  } else if (screensaverCycleState.value.isOnRandomStep) {
    // Currently on RANDOM, switch to FLIPGRID
    screensaverCycleState.value.isOnRandomStep = false;
    screensaverCycleState.value.isOnFlipGridStep = true;

    // Trigger FLIPGRID button functionality with smooth transition
    if (galleryInstance.value) {
      galleryInstance.value.switchToCSS3DMode();
      galleryInstance.value.layoutManager.switchToLayout("flipGrid", true);
    }
  } else if (screensaverCycleState.value.isOnFlipGridStep) {
    // Currently on FLIPGRID, switch to next PHOTOS1 sub-button
    screensaverCycleState.value.isOnFlipGridStep = false;
    screensaverCycleState.value.currentPhotos1Index++;

    // Check if we've reached the end of PHOTOS1 sub-buttons, wrap around to first
    if (screensaverCycleState.value.currentPhotos1Index >= photos1Subsets.value.length) {
      screensaverCycleState.value.currentPhotos1Index = 0;
    }

    const nextIndex = screensaverCycleState.value.currentPhotos1Index;

    // Trigger the next PHOTOS1 sub-button
    handlePhotos1SubButtonClick(nextIndex, true); // true = automated call
  }
};

const stopScreensaverMode = () => {
  // Clear timers
  if (screensaverTimer.value) {
    clearTimeout(screensaverTimer.value);
    screensaverTimer.value = null;
  }

  if (screensaverCountdownInterval.value) {
    clearInterval(screensaverCountdownInterval.value);
    screensaverCountdownInterval.value = null;
  }

  if (screensaverCycleState.value.cycleTimer) {
    clearTimeout(screensaverCycleState.value.cycleTimer);
    screensaverCycleState.value.cycleTimer = null;
  }

  // Reset state
  isScreensaverActive.value = false;
  screensaverCountdown.value = 0;
  screensaverCycleState.value = {
    currentPhotos1Index: 0,
    isOnRandomStep: false,
    isOnFlipGridStep: false,
    cycleTimer: null,
  };

  // Note: Do not reset activeSubButton state here as it should persist
  // when users manually click sub-buttons to stop screensaver mode.
  // The activeSubButton state should only be reset when main layout buttons are clicked.
};

// Pause screensaver countdown timer when entering focus mode
const pauseScreensaverTimer = () => {
  if (isScreensaverActive.value && screensaverCountdownInterval.value) {
    // Clear the countdown interval to pause the timer
    clearInterval(screensaverCountdownInterval.value);
    screensaverCountdownInterval.value = null;
  }
};

// Resume screensaver countdown timer when exiting focus mode
const resumeScreensaverTimer = () => {
  if (isScreensaverActive.value && !screensaverCountdownInterval.value) {
    // Resume the countdown timer from where it left off
    screensaverCountdownInterval.value = setInterval(() => {
      screensaverCountdown.value--;

      if (screensaverCountdown.value <= 0) {
        // Execute next step in cycling sequence
        executeNextScreensaverStep();
      }
    }, 1000);
  }
};

// Grade mode functions
const activateGradeMode = () => {
  // Set grade mode as active for visual feedback
  isGradeModeActive.value = true;

  // Stop screensaver mode if it's active (same as PHOTOS2 button)
  stopScreensaverMode();

  // Reset sub-button navigation state when switching to grade mode
  resetActiveSubButton();

  // Method 1: Try to simulate actual PHOTOS2 button click
  const photos2Btn = document.getElementById("photos2");
  if (photos2Btn) {
    photos2Btn.click();
  } else {
    // Method 2: Direct method calls (fallback)
    if (galleryInstance.value) {
      galleryInstance.value.switchToCSS3DMode();

      // Small delay to ensure CSS3D mode switch completes
      setTimeout(() => {
        galleryInstance.value.switchToDataset(photos2.value, "grouped", "photos2");
      }, 50);
    }
  }

  // Reset grade mode state after a short delay (visual feedback)
  setTimeout(() => {
    isGradeModeActive.value = false;
  }, 1000);
};

// Theme mode function - directly duplicates PHOTOS4 button functionality
const activateThemeMode = () => {
  // Set theme mode as active for visual feedback
  isThemeModeActive.value = true;

  // Stop screensaver mode if it's active (same as PHOTOS4 button)
  stopScreensaverMode();

  // Reset sub-button navigation state when switching to theme mode
  resetActiveSubButton();

  // Method 1: Try to simulate actual PHOTOS4 button click
  const photos4Btn = document.getElementById("photos4");
  if (photos4Btn) {
    photos4Btn.click();
  } else {
    // Method 2: Direct method calls (fallback)
    if (galleryInstance.value) {
      galleryInstance.value.switchToCSS3DMode();

      // Small delay to ensure CSS3D mode switch completes
      setTimeout(() => {
        galleryInstance.value.switchToDataset(photos4.value, "grouped4", "photos4");
      }, 50);
    }
  }

  // Reset theme mode state after visual feedback delay
  setTimeout(() => {
    isThemeModeActive.value = false;
  }, 1000);
};

// Focus mode navigation functions
const enterPhotos3Mode = () => {
  // Stop screensaver if active (match PHOTOS 3 button behavior)
  stopScreensaverMode();

  // Reset sub-button navigation state when switching to PHOTOS3 mode
  resetActiveSubButton();

  // Exit focus mode first
  if (galleryInstance.value && galleryInstance.value.layoutManager && galleryInstance.value.layoutManager.isFocusMode) {
    galleryInstance.value.layoutManager.exitFocusMode();
  }

  // Switch to photos3 dataset and apply grid layout
  if (galleryInstance.value) {
    galleryInstance.value.switchToCSS3DMode(); // Match PHOTOS 3 button behavior

    galleryInstance.value.switchToDataset(photos3.value, "grid", "photos3");

    // Verify the switch was successful
    setTimeout(() => {}, 100);
  }

  // Hide focus mode buttons
  isFocusModeActive.value = false;
};

// Focus mode navigation function for PHOTOS 5 mode
const enterPhotos5Mode = () => {
  // Stop screensaver if active (match PHOTOS 5 button behavior)
  stopScreensaverMode();

  // Reset sub-button navigation state when switching to PHOTOS5 mode
  resetActiveSubButton();

  // Exit focus mode first
  if (galleryInstance.value && galleryInstance.value.layoutManager && galleryInstance.value.layoutManager.isFocusMode) {
    galleryInstance.value.layoutManager.exitFocusMode();
  }

  // Switch to photos5 dataset and apply grid layout with fly-in animations
  if (galleryInstance.value) {
    galleryInstance.value.switchToCSS3DMode(); // Critical: Match PHOTOS 5 button behavior

    // Add small delay to ensure focus mode exit completes
    setTimeout(() => {
      galleryInstance.value.switchToDataset(photos5.value, "grid", "photos5");

      // Enhanced verification with template debugging
      setTimeout(() => {
        const currentDataset = galleryInstance.value?.currentDataset;
        const isPhotos5 = currentDataset === photos5.value;
      }, 100);
    }, 50); // Small delay to ensure clean state transition
  }

  // Hide focus mode buttons
  isFocusModeActive.value = false;
};

const exitFocusMode = () => {
  // Call the layout manager's exit focus mode function
  if (galleryInstance.value && galleryInstance.value.layoutManager && galleryInstance.value.layoutManager.isFocusMode) {
    galleryInstance.value.layoutManager.exitFocusMode();
  }

  // Hide focus mode buttons
  isFocusModeActive.value = false;
};

// PHOTOS1 sub-button handler function
const handlePhotos1SubButtonClick = (subsetIndex, isAutomatedCall = false) => {
  // Update active sub-button state
  activeSubButton.value = {
    type: "photos1",
    index: subsetIndex,
  };

  // Only stop screensaver mode if this is a manual click (not automated)
  if (!isAutomatedCall) {
    stopScreensaverMode();
  }

  // Get the specific photo subset
  const photoSubset = photos1SubsetArrays.value[subsetIndex];

  if (galleryInstance.value) {
    galleryInstance.value.switchToCSS3DMode();

    galleryInstance.value.switchToDataset(photoSubset, "grid", "photos1");
  }
};

// PHOTOS2 sub-button handler function
const handlePhotos2SubButtonClick = (subsetIndex, isAutomatedCall = false) => {
  // Update active sub-button state
  activeSubButton.value = {
    type: "photos2",
    index: subsetIndex,
  };

  // Only stop screensaver mode if this is a manual click (not automated)
  if (!isAutomatedCall) {
    stopScreensaverMode();
  }

  // Get the specific category subset
  const categorySubset = photos2SubsetArrays[subsetIndex];

  if (galleryInstance.value) {
    galleryInstance.value.switchToCSS3DMode();

    galleryInstance.value.switchToDataset(categorySubset, "grouped", "photos2");
  }
};

// PHOTOS3 sub-button handler function
const handlePhotos3SubButtonClick = (subsetIndex, isAutomatedCall = false) => {
  // Update active sub-button state
  activeSubButton.value = {
    type: "photos3",
    index: subsetIndex,
  };

  // Only stop screensaver mode if this is a manual click (not automated)
  if (!isAutomatedCall) {
    stopScreensaverMode();
  }

  // Get the specific photo subset
  const photoSubset = photos3SubsetArrays.value[subsetIndex];

  if (galleryInstance.value) {
    galleryInstance.value.switchToCSS3DMode();

    galleryInstance.value.switchToDataset(photoSubset, "grid", "photos3");
  }
};

// PHOTOS4 sub-button handler function
const handlePhotos4SubButtonClick = (subsetIndex, isAutomatedCall = false) => {
  // Update active sub-button state
  activeSubButton.value = {
    type: "photos4",
    index: subsetIndex,
  };

  // Only stop screensaver mode if this is a manual click (not automated)
  if (!isAutomatedCall) {
    stopScreensaverMode();
  }

  // Get the specific category subset
  const categorySubset = photos4SubsetArrays[subsetIndex];

  if (galleryInstance.value) {
    galleryInstance.value.switchToCSS3DMode();

    galleryInstance.value.switchToDataset(categorySubset, "grouped4", "photos4");
  }
};

// PHOTOS5 sub-button handler function
const handlePhotos5SubButtonClick = (subsetIndex, isAutomatedCall = false) => {
  // Update active sub-button state
  activeSubButton.value = {
    type: "photos5",
    index: subsetIndex,
  };

  // Only stop screensaver mode if this is a manual click (not automated)
  if (!isAutomatedCall) {
    stopScreensaverMode();
  }

  // Get the specific photo subset
  const photoSubset = photos5SubsetArrays.value[subsetIndex];

  if (galleryInstance.value) {
    galleryInstance.value.switchToCSS3DMode();

    galleryInstance.value.switchToDataset(photoSubset, "grid", "photos5");
  }
};

// Sub-button navigation functions
const getSubButtonCount = (type) => {
  switch (type) {
    case "photos1":
      return photos1Subsets.value.length;
    case "photos2":
      return photos2Subsets.value.length;
    case "photos3":
      return photos3Subsets.value.length;
    case "photos4":
      return photos4Subsets.value.length;
    case "photos5":
      return photos5Subsets.value.length;
    default:
      return 0;
  }
};

const canNavigateSubButton = (direction) => {
  if (activeSubButton.value.type === null) return false;

  const count = getSubButtonCount(activeSubButton.value.type);
  const currentIndex = activeSubButton.value.index;

  if (direction === "prev") {
    return count > 1; // Can always navigate if there's more than one button
  } else if (direction === "next") {
    return count > 1; // Can always navigate if there's more than one button
  }

  return false;
};

const navigateSubButton = (direction) => {
  if (!canNavigateSubButton(direction)) return;

  const count = getSubButtonCount(activeSubButton.value.type);
  const currentIndex = activeSubButton.value.index;
  let newIndex;

  if (direction === "prev") {
    newIndex = currentIndex === 0 ? count - 1 : currentIndex - 1;
  } else if (direction === "next") {
    newIndex = currentIndex === count - 1 ? 0 : currentIndex + 1;
  } else {
    return;
  }

  // Call the appropriate sub-button handler
  switch (activeSubButton.value.type) {
    case "photos1":
      handlePhotos1SubButtonClick(newIndex);
      break;
    case "photos2":
      handlePhotos2SubButtonClick(newIndex);
      break;
    case "photos3":
      handlePhotos3SubButtonClick(newIndex);
      break;
    case "photos4":
      handlePhotos4SubButtonClick(newIndex);
      break;
    case "photos5":
      handlePhotos5SubButtonClick(newIndex);
      break;
  }
};

// Function to reset active sub-button state (called when main buttons are clicked)
const resetActiveSubButton = () => {
  activeSubButton.value = {
    type: null,
    index: -1,
  };
};

// Function to handle manual interruption of screensaver (called by other button clicks)
const interruptScreensaverIfActive = () => {
  if (isScreensaverActive.value) {
    stopScreensaverMode();
  }
};

// Keyboard navigation for sub-buttons
const handleSubButtonKeyNavigation = (event) => {
  if (activeSubButton.value.type === null) return;

  // Only handle arrow keys when a sub-button is active
  if (event.key === "ArrowLeft" || event.key === "ArrowUp") {
    event.preventDefault();
    navigateSubButton("prev");
  } else if (event.key === "ArrowRight" || event.key === "ArrowDown") {
    event.preventDefault();
    navigateSubButton("next");
  }
};

class CSS3DPhotoGallery {
  constructor() {
    if (CSS3DPhotoGallery.instance) return CSS3DPhotoGallery.instance;
    CSS3DPhotoGallery.instance = this;

    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.objects = [];

    // Layout manager instance
    this.layoutManager = null;

    // Current dataset reference
    this.currentDataset = photos.value;
    this.currentFlatDataset = photos.value; // For flat datasets, this is the same

    // Store reference in Vue reactive data
    galleryInstance.value = this;

    this.init();
  }
  init() {
    const container = document.querySelector("#SceneCon");
    if (!container) {
      return;
    }

    const WD = container.clientWidth;
    const WH = container.clientHeight;

    // Initialize camera with fixed position (no controls)
    this.camera = new THREE.PerspectiveCamera(40, WD / WH, 1, 10000);
    this.camera.position.set(0, 0, 2000);
    this.camera.rotation.set(0, 0, 0);

    // Initialize scene
    this.scene = new THREE.Scene();

    // Create photo elements and CSS3D objects
    this.createPhotoElements();

    // Initialize CSS3D renderer
    try {
      this.renderer = new CSS3DRenderer();
      this.renderer.setSize(WD, WH);
      container.appendChild(this.renderer.domElement);
    } catch (error) {
      return;
    }

    // Initialize layout manager with focus mode callbacks
    const focusCallbacks = {
      onEnterFocus: (imageIndex, focusedObject, currentLayout) => {
        // Enable focus mode UI for grouped layouts (PHOTOS2/PHOTOS4)
        if (currentLayout === "grouped" || currentLayout === "grouped4") {
          isFocusModeActive.value = true;
          focusedImageData.value = { imageIndex, focusedObject };
        }
        // Note: All layouts now use unified focus behavior - click outside focused element to exit focus mode

        // Pause screensaver countdown timer when entering focus mode
        pauseScreensaverTimer();
      },
      onExitFocus: (wasSpecialLayout) => {
        isFocusModeActive.value = false;
        focusedImageData.value = null;

        // Resume screensaver countdown timer when exiting focus mode
        resumeScreensaverTimer();
      },
    };

    this.layoutManager = new LayoutManager(this.objects, this.camera, () => this.render(), focusCallbacks, this.scene);

    // Set category title label template for PHOTOS4 mode
    if (categoryTitleLabelRef.value && categoryTitleLabelRef.value.getTemplateElement) {
      this.layoutManager.setCategoryTitleLabelTemplate(categoryTitleLabelRef.value.getTemplateElement());
    }

    // Set category title label2 template for PHOTOS2 mode
    if (categoryTitleLabel2Ref.value && categoryTitleLabel2Ref.value.getTemplateElement) {
      this.layoutManager.setCategoryTitleLabel2Template(categoryTitleLabel2Ref.value.getTemplateElement());
    }

    // Setup event listeners
    this.setupEventListeners();

    // Initialize button states (photos is the default dataset)
    this.updateDatasetButtonStates(photos);

    // Start with grid layout
    this.layoutManager.applyLayoutClasses("grid"); // Apply initial layout classes
    this.layoutManager.transform(this.layoutManager.getTargets().grid, false); // No transition for initial setup

    // Start animation loop
    this.animate();

    // Handle window resize
    window.addEventListener("resize", () => this.resize());

    // Handle keyboard events for focus mode and layout switching
    window.addEventListener("keydown", (event) => {
      // Handle layout manager events first
      if (this.layoutManager) {
        this.layoutManager.handleKeyDown(event);
      }

      // Add keyboard shortcuts for layout switching
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case "1":
            event.preventDefault();
            document.getElementById("grid")?.click();
            break;
          case "2":
            event.preventDefault();
            document.getElementById("random")?.click();
            break;
          case "3":
            event.preventDefault();
            document.getElementById("swiper")?.click();
            break;
          case "s":
            event.preventDefault();
            toggleScreensaverMode();
            break;
        }
      }
    });
  }

  createPhotoElements() {
    // Use flattened dataset if available, otherwise use current dataset
    const datasetToUse = this.currentFlatDataset || this.currentDataset;

    // Determine template type based on current mode state (not data structure)
    let templateId;
    let templateType;

    // 优先使用全局模式状态来确定模板类型
    if (currentMode.value.templateType) {
      templateType = currentMode.value.templateType;

      // 根据模板类型设置对应的模板ID
      switch (templateType) {
        case "photos1":
          templateId = "photo-frame-template-photos1";
          break;
        case "photos3":
          templateId = "photo-frame-template-photos3";
          break;
        case "photos5":
          templateId = "photo-frame-template-photos5";
          break;
        case "grouped":
          templateId = "photo-frame-template-grouped";
          break;
        case "grouped4":
          templateId = "photo-frame-template-grouped4";
          break;
        default:
          templateId = "photo-frame-template";
          templateType = "standard";
      }
    } else {
      // 如果没有明确的模式状态，回退到原来的数据结构检测逻辑
      // Enhanced PHOTOS5 detection with multiple checks
      const isPhotos5Dataset =
        this.currentDataset === photos5.value ||
        (this.currentDataset &&
          photos5.value &&
          this.currentDataset.length === photos5.value.length &&
          this.currentDataset[0]?.id === photos5.value[0]?.id &&
          this.currentDataset[0]?.title === photos5.value[0]?.title);

      // Enhanced PHOTOS1 detection with precise, mutually exclusive checks
      const isPhotos1Dataset = this.detectPhotos1Dataset(this.currentDataset);

      // Enhanced PHOTOS3 detection with precise, mutually exclusive checks
      const isPhotos3Dataset = this.detectPhotos3Dataset(this.currentDataset);

      // Check if this is a category-based dataset (grouped structure)
      const isCategoryBasedDataset =
        this.currentDataset &&
        this.currentDataset.length > 0 &&
        this.currentDataset[0] &&
        typeof this.currentDataset[0] === "object" &&
        this.currentDataset[0].hasOwnProperty("category") &&
        this.currentDataset[0].hasOwnProperty("photos") &&
        Array.isArray(this.currentDataset[0].photos);

      if (isPhotos5Dataset) {
        // PHOTOS 5 uses its own dedicated template
        templateId = "photo-frame-template-photos5";
        templateType = "photos5";
      } else if (this.currentDataset === photos4.value || this.isPhotos4Subset(this.currentDataset)) {
        // PHOTOS 4 and its subsets use dedicated PHOTOS4 template
        templateId = "photo-frame-template-grouped4";
        templateType = "grouped4";
      } else if (this.currentDataset === photos2.value || isCategoryBasedDataset) {
        // PHOTOS 2 and other category-based datasets use grouped layout template
        templateId = "photo-frame-template-grouped";
        templateType = "grouped";
      } else if (isPhotos1Dataset) {
        // PHOTOS 1 and its subsets use dedicated PHOTOS1 template
        templateId = "photo-frame-template-photos1";
        templateType = "photos1";
      } else if (isPhotos3Dataset) {
        // PHOTOS 3 and its subsets use dedicated PHOTOS3 template
        templateId = "photo-frame-template-photos3";
        templateType = "photos3";
      } else {
        // GRID, RANDOM use standard template
        templateId = "photo-frame-template";
        templateType = "standard";
      }
    }

    // Validate template availability and apply fallback if necessary
    // 为了兼容性，我们需要定义这些变量（即使在新的模式下可能不需要）
    const isPhotos1Dataset = currentMode.value.templateType === "photos1" || this.detectPhotos1Dataset(this.currentDataset);
    const isPhotos3Dataset = currentMode.value.templateType === "photos3" || this.detectPhotos3Dataset(this.currentDataset);

    const templateValidation = this.validateAndGetTemplate(templateId, templateType, isPhotos1Dataset, isPhotos3Dataset);
    if (!templateValidation.success) {
      console.error(`❌ Template validation failed: ${templateValidation.error}`);
      return;
    }

    const template = templateValidation.template;
    templateId = templateValidation.templateId;
    templateType = templateValidation.templateType;
    console.log("🚀 ~ CSS3DPhotoGallery ~ createPhotoElements ~ templateType:", templateType);

    // Track successful element creation for error recovery
    let successfulElements = 0;
    let failedElements = 0;
    const errors = [];

    for (let i = 0; i < datasetToUse.length; i++) {
      try {
        const photo = datasetToUse[i];

        // Validate photo data before processing
        if (!photo || typeof photo !== "object") {
          throw new Error(`Invalid photo data at index ${i}: ${typeof photo}`);
        }

        // Clone the template element with error handling
        const element = template.cloneNode(true);
        if (!element) {
          throw new Error(`Failed to clone template ${templateId}`);
        }

        // Remove the template ID and make it visible
        element.removeAttribute("id");
        element.style.display = "";

        // Optimize for 3D transforms - enable hardware acceleration
        element.style.willChange = "transform";

        // Add template type identifier for debugging
        element.setAttribute("data-template-source", templateId);
        element.setAttribute("data-element-index", i);

        // Populate the cloned element with dynamic data based on template type
        if (templateType === "photos5") {
          // PHOTOS 5 dedicated template elements
          const numberOverlay = element.querySelector(".photo-number-overlay-photos5");
          const rowIndicator = element.querySelector(".photo-row-indicator");
          const img = element.querySelector(".photo-image-photos5");
          const title = element.querySelector(".photo-title-photos5");
          const description = element.querySelector(".photo-description-photos5");
          const gridPosition = element.querySelector(".photo-grid-position");

          // Set numerical identifier overlay
          if (numberOverlay) {
            // Calculate row number for 5-row layout (will be updated when layout is set)
            const estimatedRow = Math.floor(i / Math.ceil(datasetToUse.length / 5)) + 1;
            numberOverlay.textContent = `Image ${i + 1} - Row ${estimatedRow}`;
            numberOverlay.setAttribute("data-image-index", i);
          }

          // Set row indicator
          if (rowIndicator) {
            const estimatedRow = Math.floor(i / Math.ceil(datasetToUse.length / 5)) + 1;
            rowIndicator.textContent = `R${estimatedRow}`;
            rowIndicator.setAttribute("data-row", estimatedRow);
          }

          // Set grid position indicator
          if (gridPosition) {
            const estimatedRow = Math.floor(i / Math.ceil(datasetToUse.length / 5)) + 1;
            const estimatedCol = (i % Math.ceil(datasetToUse.length / 5)) + 1;
            gridPosition.textContent = `${estimatedRow}-${estimatedCol}`;
            gridPosition.setAttribute("data-position", `${estimatedRow}-${estimatedCol}`);
          }

          // Set image data
          if (img) {
            img.src = photo.src;
            img.alt = photo.title;
          }

          // Set title
          if (title) {
            title.textContent = photo.title;
          }

          // Set description
          if (description) {
            description.textContent = photo.description;
          }
        } else if (templateType === "grouped") {
          // PHOTOS 2 (grouped) template elements - Text content instead of images
          const textMain = element.querySelector(".photo-text-main-grouped");
          const textClass = element.querySelector(".photo-text-class-grouped");
          const textName = element.querySelector(".photo-text-name-grouped");
          const textStatValues = element.querySelectorAll(".photo-text-stat-value-grouped");
          const classname = element.querySelector(".classname");
          const student = element.querySelector(".student");
          // Set main text content
          if (textMain) {
            textMain.textContent = photo.title || `内容 ${i + 1}`;
          }
          if (classname) {
            classname.textContent = photo.nianjiB;
          }
          if (student) {
            student.textContent = photo.student_count + "名";
          }
          // Set class information
          if (textClass) {
            textClass.textContent = photo.categoryTitle || "默认班级";
          }

          // Set name information
          if (textName) {
            textName.textContent = photo.description || `姓名 ${i + 1}`;
          }

          // Set statistical values
          if (textStatValues.length >= 2) {
            textStatValues[0].textContent = Math.floor(Math.random() * 100) + 1; // Random value 1-100
            textStatValues[1].textContent = Math.floor(Math.random() * 50) + 1; // Random value 1-50
          }

          // Add event listeners for focus navigation buttons in grouped template (PHOTOS 2)
          const enterBtn = element.querySelector(".focus-nav-btn.enter-btn");
          const returnBtn = element.querySelector(".focus-nav-btn.return-btn");

          if (enterBtn) {
            enterBtn.addEventListener("click", (e) => {
              e.stopPropagation(); // Prevent triggering the photo click event
              console.log("enterBtn", photo);
              getStudentList({ sid: 92, campus_id: 105, class_id: photo.id }).then((res) => {
                console.log("res", res);
                if (res.data.code == 200) {
                  // Transform API data to photos3 format
                  photos3.value = res.data.data.map((student, index) => ({
                    id: student.student_number || index + 1,
                    src: `https://picsum.photos/1500/1700?random=${200 + index}`, // 使用占位图片
                    title: student.student_number ? `${student.student_number}号` : `${index + 1}号`,
                    description: student.name,
                    xb: student.sex === 1 ? "女" : "男", // 0=男, 1=女
                    nianjiB: student.nianjiB,
                  }));
                  console.log("photos3 transformed:", photos3.value);

                  if (this.currentDataset === photos2.value) {
                    enterPhotos3Mode();
                  } else {
                    // For PHOTOS 2 subsets, also navigate to PHOTOS 3
                    enterPhotos3Mode();
                  }
                }
              });
              // Dataset-aware navigation logic for PHOTOS 2
            });
          }

          if (returnBtn) {
            returnBtn.addEventListener("click", (e) => {
              e.stopPropagation(); // Prevent triggering the photo click event
              exitFocusMode();
            });
          }
        } else if (templateType === "grouped4") {
          // PHOTOS 4 (grouped4) template elements - Text-only implementation (following PHOTOS2 pattern)
          const categoryBadge = element.querySelector(".photo-category-badge");
          const numberOverlay = element.querySelector(".photo-number-overlay-grouped");
          const title = element.querySelector(".photo-title-grouped");
          const description = element.querySelector(".photo-description-grouped");

          console.log("photo", photo);
          // Set category badge
          if (categoryBadge && photo.categoryTitle) {
            categoryBadge.textContent = photo.categoryTitle;
            categoryBadge.setAttribute("data-category", photo.category);
          }

          // Set numerical identifier overlay
          if (numberOverlay) {
            // numberOverlay.textContent = `Content ${i + 1} - ${photo.categoryTitle}`;
            // numberOverlay.setAttribute("data-content-index", i);
          }

          // Set title (text content only)
          if (title) {
            title.textContent = photo.title;
          }

          // Set description (text content only)
          if (description) {
            description.textContent = photo.description;
          }

          // Add event listeners for focus navigation buttons in grouped template
          const enterBtn = element.querySelector(".focus-nav-btn.enter-btn");
          const returnBtn = element.querySelector(".focus-nav-btn.return-btn");

          if (enterBtn) {
            enterBtn.addEventListener("click", (e) => {
              e.stopPropagation(); // Prevent triggering the photo click event

              // Dataset-aware navigation logic
              if (this.currentDataset === photos2.value) {
                // For PHOTOS2 mode, get student list data before entering PHOTOS3 mode
                getStudentList({ sid: 92, campus_id: 105, class_id: photo.id }).then((res) => {
                  console.log("res", res);
                  if (res.data.code == 200) {
                    // Transform API data to photos3 format
                    photos3.value = res.data.data.map((student, index) => ({
                      id: student.student_number || index + 1,
                      src: `https://picsum.photos/1500/1700?random=${200 + index}`, // 使用占位图片
                      title: student.student_number ? `${student.student_number}号` : `${index + 1}号`,
                      description: student.name,
                      xb: student.sex === 1 ? "女" : "男", // 0=男, 1=女
                      nianjiB: student.nianjiB,
                    }));
                    console.log("photos3 transformed:", photos3.value);
                    enterPhotos3Mode();
                  }
                });
              } else if (this.currentDataset === photos4.value) {
                //需要参数
                const params = {};
                getByGradeInfo(params, "theme").then(() => {
                  enterPhotos5Mode();
                });
              } else {
                enterPhotos3Mode();
              }
            });
          }

          if (returnBtn) {
            returnBtn.addEventListener("click", (e) => {
              e.stopPropagation(); // Prevent triggering the photo click event
              exitFocusMode();
            });
          }
        } else if (templateType === "photos1") {
          // PHOTOS1 dedicated template elements
          this.populatePhotos1Element(element, photo, i, datasetToUse.length);
        } else if (templateType === "photos3") {
          console.log("photos3", photo);
          // PHOTOS3 dedicated template elements
          this.populatePhotos3Element(element, photo, i, datasetToUse.length);
        } else {
          // Standard template elements (GRID, RANDOM modes)
          const numberOverlay = element.querySelector(".photo-number-overlay");
          const img = element.querySelector(".photo-image");
          const title = element.querySelector(".photo-title");
          const description = element.querySelector(".photo-description");

          // Set numerical identifier overlay
          if (numberOverlay) {
            // Calculate row number (will be updated when layout is set, but provide initial estimate)
            // const estimatedRow = Math.floor(i / Math.ceil(datasetToUse.length / 3)) + 1;
            // numberOverlay.textContent = `Image ${i + 1} - Row ${estimatedRow}`;
            // numberOverlay.setAttribute("data-image-index", i);
          }

          // Set image data
          if (img) {
            img.src = photo.src;
            img.alt = photo.title;
          }

          // Set title
          if (title) {
            title.textContent = photo.title;
          }

          // Set description
          if (description) {
            description.textContent = photo.description;
          }
        }

        // Create CSS3D object
        const object = new CSS3DObject(element);
        object.position.x = Math.random() * 2000 - 1000;
        object.position.y = Math.random() * 2000 - 1000;
        object.position.z = Math.random() * 2000 - 1000;

        // Store reference to the number overlay for later updates
        let overlayElement;
        if (templateType === "photos5") {
          overlayElement = element.querySelector(".photo-number-overlay-photos5");
        } else if (templateType === "grouped" || templateType === "grouped4") {
          overlayElement = element.querySelector(".photo-number-overlay-grouped");

          console.log("overlayElement", photo);
        } else {
          // For photos1, photos3, and standard templates, all use .photo-number-overlay
          overlayElement = element.querySelector(".photo-number-overlay");
        }

        object.userData = {
          // numberOverlay: overlayElement,
          imageIndex: i,
          templateType: templateType,
          isGrouped: templateType === "grouped" || templateType === "grouped4",
          isPhotos5: templateType === "photos5",
          category: photo.category || null,
          categoryTitle: photo.categoryTitle || null,
        };

        // Add click event listener with special handling for PHOTOS 3 mode
        element.addEventListener("click", () => {
          // Special behavior for PHOTOS 3 mode: open Swiper gallery modal
          // Enhanced check to ensure we're definitely in PHOTOS 3 mode

          const isPhotos3Mode =
            this.currentDataset === photos3.value ||
            (this.currentDataset && photos3.value && this.currentDataset.length === photos3.value.length && this.currentDataset[0]?.id === photos3.value[0]?.id);

          if (isPhotos3Mode) {
            // Switch to Swiper mode which will use SWIPERImg dataset
            this.switchToSwiperMode();
            selectStudentId.value = photo.id;

            // Always show the third element (index 2) when opening SWIPER from PHOTOS 3 mode
            setTimeout(() => {
              if (swiperGalleryRef.value && swiperGalleryRef.value.goToSlide) {
                const targetSlide = 1; // Always go to the third element (index 2)
                swiperGalleryRef.value.goToSlide(targetSlide);
              }
            }, 200); // Small delay to ensure carousel is initialized

            return; // Skip normal layout manager handling
          }

          // Normal handling for other modes
          this.layoutManager.handleImageClick(i, object);
        });

        this.scene.add(object);
        this.objects.push(object);
        successfulElements++;
      } catch (error) {
        failedElements++;
        const errorMessage = `Failed to create element ${i + 1}: ${error.message}`;
        errors.push(errorMessage);
        console.error(`❌ ${errorMessage}`, error);

        // Continue with next element unless it's a critical failure
        if (error.message.includes("template validation failed") || error.message.includes("No photo frame templates available")) {
          console.error(`🚨 Critical template failure detected, stopping element creation`);
          break;
        }
      }
    }

    // Report element creation results

    if (errors.length > 0) {
      console.warn(`⚠️ Errors encountered:`, errors);
    }

    if (successfulElements === 0) {
      console.error(`🚨 No elements were created successfully. Template system may be broken.`);
      return;
    }

    // Created CSS3D objects successfully
  }

  // Old layout methods removed - now handled by LayoutManager

  /**
   * Flatten nested photo dataset structure for element creation
   */
  flattenPhotoDataset(dataset) {
    // If dataset is already flat (like photos), return as is
    if (dataset.length > 0 && dataset[0].src) {
      return dataset;
    }

    // If dataset is nested (like photos2), flatten it
    const flattened = [];
    dataset.forEach((categoryGroup) => {
      if (categoryGroup.photos) {
        categoryGroup.photos.forEach((photo) => {
          // Add category information back to each photo for reference
          flattened.push({
            ...photo,
            category: categoryGroup.category,
            categoryTitle: categoryGroup.title,
          });
        });
      }
    });
    return flattened;
  }

  /**
   * Populate PHOTOS1 template element with photo data
   */
  populatePhotos1Element(element, photo, index, totalCount) {
    try {
      const numberOverlay = element.querySelector(".photo-number-overlay");
      const img = element.querySelector(".photo-image");
      const title = element.querySelector(".photo-frame-photos1ChiText1");
      const class_name = element.querySelector(".photo-frame-photos1ChiText21");
      const student_name = element.querySelector(".photo-frame-photos1ChiText22");

      const like = element.querySelector(".photos1ChiText231Text");
      const likeDom = element.querySelector(".likeDom");

      const praise = element.querySelector(".photos1ChiText231Text123");
      const praiseDom = element.querySelector(".photos1ChiText2312");

      const description = element.querySelector(".photo-description");

      // Set numerical identifier overlay with PHOTOS1-specific labeling
      if (numberOverlay) {
        const estimatedRow = Math.floor(index / Math.ceil(totalCount / 3)) + 1;
        numberOverlay.textContent = `PHOTOS1 Image ${index + 1} - Row ${estimatedRow}`;
        numberOverlay.setAttribute("data-image-index", index);
        numberOverlay.setAttribute("data-template-type", "photos1");
      }

      // Set image data with PHOTOS1-specific validation
      if (img && photo.src) {
        img.src = photo.src;
        img.alt = photo.title || `PHOTOS1 Image ${index + 1}`;
        img.setAttribute("data-photos1-index", index);
      }

      // Set title with PHOTOS1-specific fallback
      if (title) {
        title.textContent = photo.title || `PHOTOS1 Image ${index + 1}`;
        title.setAttribute("data-template-type", "photos1");
      }

      if (class_name) {
        class_name.textContent = photo.nianji + photo.banji;
      }

      if (student_name) {
        student_name.textContent = photo.student_name;
      }

      if (like) {
        like.textContent = photo.like || 0;

        likeDom.addEventListener("click", () => {
          likeArtwork({ sid: 92, campus_id: 105, artwork_id: photo.id, if_like: 0 }).then((res) => {
            console.log(res);
            if (res.data.code == 200) {
              like.textContent = parseInt(like.textContent) + 1;
            }
          });
        });
      }

      if (praise) {
        praise.textContent = photo.praise;
        praiseDom.addEventListener("click", () => {
          likeArtwork({ sid: 92, campus_id: 105, artwork_id: photo.id, if_like: 1 }).then((res) => {
            if (res.data.code == 200) {
              praise.textContent = parseInt(praise.textContent) + 1;
            }
          });
        });
      }

      // Set description with PHOTOS1-specific fallback
      if (description) {
        description.textContent = photo.description || `PHOTOS1 content ${index + 1}`;
        description.setAttribute("data-template-type", "photos1");
      }
    } catch (error) {
      console.error(`❌ Error populating PHOTOS1 element ${index + 1}:`, error);
      throw new Error(`PHOTOS1 element population failed: ${error.message}`);
    }
  }

  /**
   * Populate PHOTOS3 template element with photo data
   */
  populatePhotos3Element(element, photo, index, totalCount) {
    try {
      console.log("populatePhotos3Element", photo);
      const numberOverlay = element.querySelector(".photo-number-overlay");
      const img = element.querySelector(".photo-image");
      const title = element.querySelector(".photo-title");
      const description = element.querySelector(".photo-description");

      // Apply conditional CSS class based on gender (xb property)
      // Add "photo-frame-photos3nu" class when xb equals "女" (female)
      if (photo.xb === "女") {
        element.classList.add("photo-frame-photos3nu");
      } else {
        // Ensure the class is removed if it was previously added
        element.classList.remove("photo-frame-photos3nu");
      }

      // Set numerical identifier overlay with PHOTOS3-specific labeling
      if (numberOverlay) {
        const estimatedRow = Math.floor(index / Math.ceil(totalCount / 3)) + 1;
        numberOverlay.textContent = `PHOTOS3 Image ${index + 1} - Row ${estimatedRow}`;
        numberOverlay.setAttribute("data-image-index", index);
        numberOverlay.setAttribute("data-template-type", "photos3");
      }

      // Set image data with PHOTOS3-specific validation
      if (img && photo.src) {
        img.src = photo.src;
        img.alt = photo.title || `PHOTOS3 Image ${index + 1}`;
        img.setAttribute("data-photos3-index", index);
      }

      // Set title with PHOTOS3-specific fallback
      if (title) {
        title.textContent = photo.title || `PHOTOS3 Image ${index + 1}`;
        title.setAttribute("data-template-type", "photos3");
      }

      // Set description with PHOTOS3-specific fallback
      if (description) {
        description.textContent = photo.description || `PHOTOS3 content ${index + 1}`;
        description.setAttribute("data-template-type", "photos3");
      }
    } catch (error) {
      console.error(`❌ Error populating PHOTOS3 element ${index + 1}:`, error);
      throw new Error(`PHOTOS3 element population failed: ${error.message}`);
    }
  }

  /**
   * Validate template availability and provide fallback options
   */
  validateAndGetTemplate(templateId, templateType, isPhotos1Dataset, isPhotos3Dataset) {
    // Special handling for component-based templates
    let template = null;
    let fallbackUsed = false;
    let fallbackReason = "";

    if (templateId === "photo-frame-template") {
      // Try to get template from Standard component
      if (standardTemplateRef.value && standardTemplateRef.value.getTemplateElement) {
        template = standardTemplateRef.value.getTemplateElement();
      } else {
        console.warn(`⚠️ Standard Component not ready, trying DOM fallback`);
        template = document.getElementById(templateId);
      }
    } else if (templateType === "photos1" && templateId === "photo-frame-template-photos1") {
      // Try to get template from PHOTOS1 component
      if (photos1TemplateRef.value && photos1TemplateRef.value.getTemplateElement) {
        template = photos1TemplateRef.value.getTemplateElement();
      } else {
        console.warn(`⚠️ PHOTOS1 Component not ready, trying DOM fallback`);
        template = document.getElementById(templateId);
      }
    } else if (templateType === "photos3" && templateId === "photo-frame-template-photos3") {
      // Try to get template from PHOTOS3 component
      if (photos3TemplateRef.value && photos3TemplateRef.value.getTemplateElement) {
        template = photos3TemplateRef.value.getTemplateElement();
      } else {
        console.warn(`⚠️ PHOTOS3 Component not ready, trying DOM fallback`);
        template = document.getElementById(templateId);
      }
    } else if (templateType === "photos5" && templateId === "photo-frame-template-photos5") {
      // Try to get template from PHOTOS5 component
      if (photos5TemplateRef.value && photos5TemplateRef.value.getTemplateElement) {
        template = photos5TemplateRef.value.getTemplateElement();
      } else {
        console.warn(`⚠️ PHOTOS5 Component not ready, trying DOM fallback`);
        template = document.getElementById(templateId);
      }
    } else if (templateType === "grouped" && templateId === "photo-frame-template-grouped") {
      // Try to get template from Grouped component
      if (groupedTemplateRef.value && groupedTemplateRef.value.getTemplateElement) {
        template = groupedTemplateRef.value.getTemplateElement();
      } else {
        console.warn(`⚠️ Grouped Component not ready, trying DOM fallback`);
        template = document.getElementById(templateId);
      }
    } else if (templateType === "grouped4" && templateId === "photo-frame-template-grouped4") {
      // Try to get template from Grouped4 component (PHOTOS4 Mode)
      if (grouped4TemplateRef.value && grouped4TemplateRef.value.getTemplateElement) {
        template = grouped4TemplateRef.value.getTemplateElement();
      } else {
        console.warn(`⚠️ Grouped4 Component not ready, trying DOM fallback`);
        template = document.getElementById(templateId);
      }
    } else {
      // Standard DOM-based template lookup for other types
      template = document.getElementById(templateId);
    }

    if (template) {
      const isComponentBased =
        templateId === "photo-frame-template" ||
        templateType === "photos1" ||
        templateType === "photos3" ||
        templateType === "photos5" ||
        templateType === "grouped" ||
        templateType === "grouped4";
      const templateSource = isComponentBased ? "Component-based" : "DOM-based";
      return {
        success: true,
        template: template,
        templateId: templateId,
        templateType: templateType,
        fallbackUsed: false,
        fallbackReason: "",
      };
    }

    // Template not found - apply fallback logic
    console.warn(`⚠️ Template not found: ${templateId}, applying fallback logic`);

    // Fallback strategy for PHOTOS1
    if (templateType === "photos1" || isPhotos1Dataset) {
      // Try standard template as fallback for PHOTOS1 (component-based first)
      if (standardTemplateRef.value && standardTemplateRef.value.getTemplateElement) {
        template = standardTemplateRef.value.getTemplateElement();
      } else {
        template = document.getElementById("photo-frame-template");
      }
      if (template) {
        fallbackUsed = true;
        fallbackReason = `PHOTOS1 template missing, using standard template`;
        return {
          success: true,
          template: template,
          templateId: "photo-frame-template",
          templateType: "standard",
          fallbackUsed: true,
          fallbackReason: fallbackReason,
        };
      }
    }

    // Fallback strategy for PHOTOS3
    if (templateType === "photos3" || isPhotos3Dataset) {
      // Try standard template as fallback for PHOTOS3 (component-based first)
      if (standardTemplateRef.value && standardTemplateRef.value.getTemplateElement) {
        template = standardTemplateRef.value.getTemplateElement();
      } else {
        template = document.getElementById("photo-frame-template");
      }
      if (template) {
        fallbackUsed = true;
        fallbackReason = `PHOTOS3 template missing, using standard template`;
        return {
          success: true,
          template: template,
          templateId: "photo-frame-template",
          templateType: "standard",
          fallbackUsed: true,
          fallbackReason: fallbackReason,
        };
      }
    }

    // Final fallback: Try any available template
    const fallbackTemplates = [
      "photo-frame-template",
      "photo-frame-template-photos1",
      "photo-frame-template-photos3",
      "photo-frame-template-grouped",
      "photo-frame-template-photos5",
    ];

    for (const fallbackId of fallbackTemplates) {
      template = document.getElementById(fallbackId);
      if (template) {
        fallbackUsed = true;
        fallbackReason = `All preferred templates missing, using ${fallbackId} as emergency fallback`;

        // Determine fallback template type
        let fallbackType = "standard";
        if (fallbackId.includes("photos1")) fallbackType = "photos1";
        else if (fallbackId.includes("photos3")) fallbackType = "photos3";
        else if (fallbackId.includes("grouped")) fallbackType = "grouped";
        else if (fallbackId.includes("photos5")) fallbackType = "photos5";

        return {
          success: true,
          template: template,
          templateId: fallbackId,
          templateType: fallbackType,
          fallbackUsed: true,
          fallbackReason: fallbackReason,
        };
      }
    }

    // Complete failure - no templates available
    return {
      success: false,
      template: null,
      templateId: null,
      templateType: null,
      fallbackUsed: false,
      error: `No photo frame templates available in DOM. Checked: ${templateId}, ${fallbackTemplates.join(", ")}`,
    };
  }

  /**
   * Precise PHOTOS1 dataset detection with mutual exclusivity
   */
  detectPhotos1Dataset(dataset) {
    if (!dataset || !Array.isArray(dataset) || dataset.length === 0) {
      return false;
    }

    // Primary check: Direct reference match
    if (dataset === photos) {
      return true;
    }

    // Secondary check: Subset validation (must be from PHOTOS1 and NOT from PHOTOS3)
    const isPhotos1Subset = this.isPhotos1Subset(dataset);
    const isPhotos3Subset = this.isPhotos3Subset(dataset);

    if (isPhotos1Subset && !isPhotos3Subset) {
      return true;
    }

    // Tertiary check: Content-based detection (only if not conflicting with PHOTOS3)
    if (!isPhotos3Subset && dataset.length <= photos.length && dataset[0]?.id === photos[0]?.id) {
      // Additional validation: ensure first few items match PHOTOS1 pattern
      const sampleSize = Math.min(3, dataset.length);
      const matchesPhotos1Pattern = dataset.slice(0, sampleSize).every((item, index) => photos[index] && item.id === photos[index].id && item.src === photos[index].src);

      if (matchesPhotos1Pattern) {
        return true;
      }
    }

    return false;
  }

  /**
   * Precise PHOTOS3 dataset detection with mutual exclusivity
   */
  detectPhotos3Dataset(dataset) {
    if (!dataset || !Array.isArray(dataset) || dataset.length === 0) {
      return false;
    }

    // Primary check: Direct reference match
    if (dataset === photos3) {
      return true;
    }

    // Secondary check: Subset validation (must be from PHOTOS3 and NOT from PHOTOS1)
    const isPhotos3Subset = this.isPhotos3Subset(dataset);
    const isPhotos1Subset = this.isPhotos1Subset(dataset);

    if (isPhotos3Subset && !isPhotos1Subset) {
      return true;
    }

    // Tertiary check: Content-based detection (only if not conflicting with PHOTOS1)
    if (!isPhotos1Subset && dataset.length <= photos3.value.length && dataset[0]?.id === photos3.value[0]?.id) {
      // Additional validation: ensure first few items match PHOTOS3 pattern
      const sampleSize = Math.min(3, dataset.length);
      const matchesPhotos3Pattern = dataset
        .slice(0, sampleSize)
        .every((item, index) => photos3.value[index] && item.id === photos3.value[index].id && item.src === photos3.value[index].src);

      if (matchesPhotos3Pattern) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if a dataset is a subset of PHOTOS1 (photos)
   */
  isPhotos1Subset(dataset) {
    if (!dataset || !Array.isArray(dataset) || dataset.length === 0) {
      return false;
    }

    // Check if all items in the dataset exist in the original photos array
    // and have the same structure (flat array with src, title, description)
    return dataset.every((item) => {
      return (
        item &&
        typeof item === "object" &&
        item.hasOwnProperty("src") &&
        item.hasOwnProperty("title") &&
        item.hasOwnProperty("description") &&
        !item.hasOwnProperty("category") && // PHOTOS1 items don't have category
        photos.value.some((originalPhoto) => originalPhoto.id === item.id && originalPhoto.src === item.src)
      );
    });
  }

  /**
   * Check if a dataset is a subset of PHOTOS3
   */
  isPhotos3Subset(dataset) {
    if (!dataset || !Array.isArray(dataset) || dataset.length === 0) {
      return false;
    }

    // Check if all items in the dataset exist in the original photos3 array
    // and have the same structure (flat array with src, title, description)
    const isSubset = dataset.every((item) => {
      const hasCorrectStructure =
        item && typeof item === "object" && item.hasOwnProperty("src") && item.hasOwnProperty("title") && item.hasOwnProperty("description") && !item.hasOwnProperty("category"); // PHOTOS3 items don't have category

      const existsInPhotos3 = photos3.value.some((originalPhoto) => originalPhoto.id === item.id && originalPhoto.src === item.src);

      return hasCorrectStructure && existsInPhotos3;
    });

    if (isSubset) {
    }

    return isSubset;
  }

  /**
   * Check if a dataset is a subset of PHOTOS4
   */
  isPhotos4Subset(dataset) {
    if (!dataset || !Array.isArray(dataset) || dataset.length === 0) {
      return false;
    }

    // Check if this is a category-based dataset (grouped structure like PHOTOS2/4)
    const isCategoryBasedDataset =
      dataset.length > 0 &&
      dataset[0] &&
      typeof dataset[0] === "object" &&
      dataset[0].hasOwnProperty("category") &&
      dataset[0].hasOwnProperty("photos") &&
      Array.isArray(dataset[0].photos);

    if (!isCategoryBasedDataset) {
      return false;
    }

    // Check if all categories in the dataset exist in the original photos4 array
    const isSubset = dataset.every((categoryGroup) => {
      return photos4.value.some((originalCategory) => originalCategory.category === categoryGroup.category && originalCategory.title === categoryGroup.title);
    });

    if (isSubset) {
    }

    return isSubset;
  }

  /**
   * Check if a dataset is a subset of PHOTOS5
   */
  isPhotos5Subset(dataset) {
    if (!dataset || !Array.isArray(dataset) || dataset.length === 0) {
      return false;
    }

    // Check if all items in the dataset exist in the original photos5 array
    // and have the same structure (flat array with src, title, description)
    return dataset.every((item) => {
      return (
        item &&
        typeof item === "object" &&
        item.hasOwnProperty("src") &&
        item.hasOwnProperty("title") &&
        item.hasOwnProperty("description") &&
        !item.hasOwnProperty("category") && // PHOTOS5 items don't have category
        photos5.value.some((originalPhoto) => originalPhoto.id === item.id && originalPhoto.src === item.src)
      );
    });
  }

  /**
   * Switch to a different photo dataset
   */
  switchToDataset(newDataset, layoutType = "grouped", modeType = null) {
    // Update current dataset reference
    this.currentDataset = newDataset;
    this.currentFlatDataset = this.flattenPhotoDataset(newDataset);

    // 更新全局模式状态
    if (modeType) {
      currentMode.value.type = modeType;
      currentMode.value.layoutType = layoutType;

      // 根据模式类型设置模板类型
      switch (modeType) {
        case "photos1":
          currentMode.value.templateType = "photos1";
          break;
        case "photos2":
          currentMode.value.templateType = "grouped";
          break;
        case "photos3":
          currentMode.value.templateType = "photos3";
          break;
        case "photos4":
          currentMode.value.templateType = "grouped4";
          break;
        case "photos5":
          currentMode.value.templateType = "photos5";
          break;
        default:
          currentMode.value.templateType = "standard";
      }
    }

    // If currently in Swiper mode, the component will automatically update via computed property
    if (isSwiperMode.value) {
      this.updateDatasetButtonStates(newDataset);
      return;
    }

    // Clear current photos from scene
    this.clearCurrentPhotos();

    // Create new photo objects
    this.createPhotoElements();

    // Setup layout manager with new objects and focus callbacks
    const focusCallbacks = {
      onEnterFocus: (imageIndex, focusedObject, currentLayout) => {
        if (currentLayout === "grouped" || currentLayout === "grouped4") {
          isFocusModeActive.value = true;
          focusedImageData.value = { imageIndex, focusedObject };
        }
        // Note: All layouts now use unified focus behavior - click outside focused element to exit focus mode

        // Pause screensaver countdown timer when entering focus mode
        pauseScreensaverTimer();
      },
      onExitFocus: (wasSpecialLayout) => {
        isFocusModeActive.value = false;
        focusedImageData.value = null;

        // Resume screensaver countdown timer when exiting focus mode
        resumeScreensaverTimer();
      },
    };

    this.layoutManager = new LayoutManager(this.objects, this.camera, () => this.render(), focusCallbacks, this.scene);

    // Set category title label template for PHOTOS4 mode
    if (categoryTitleLabelRef.value && categoryTitleLabelRef.value.getTemplateElement) {
      this.layoutManager.setCategoryTitleLabelTemplate(categoryTitleLabelRef.value.getTemplateElement());
    }

    // Set category title label2 template for PHOTOS2 mode
    if (categoryTitleLabel2Ref.value && categoryTitleLabel2Ref.value.getTemplateElement) {
      this.layoutManager.setCategoryTitleLabel2Template(categoryTitleLabel2Ref.value.getTemplateElement());
    }

    // Set the current dataset for PHOTOS3 mode detection
    this.layoutManager.setCurrentDataset(newDataset);

    // Setup the requested layout
    if (layoutType === "grouped") {
      // PHOTOS2 grouped layout with fly-in animation
      // Set layout state BEFORE setupGroupedLayout to ensure correct spacing detection
      this.layoutManager.currentLayout = "grouped";
      this.layoutManager.setupGroupedLayout(newDataset);
      this.layoutManager.setupGroupedFlyInAnimation();
      this.layoutManager.isGroupedFlyInActive = true;
      this.layoutManager.groupedFlyInStartTime = Date.now();

      this.layoutManager.switchToLayout("grouped", true);
    } else if (layoutType === "grouped4") {
      // PHOTOS4 grouped layout with fly-in animation (使用与PHOTOS2完全相同的行为)
      // Set layout state BEFORE setupGroupedLayout to ensure correct spacing detection
      this.layoutManager.currentLayout = "grouped4";
      this.layoutManager.setupGroupedLayout(newDataset);
      this.layoutManager.setupGrouped4FlyInAnimation();
      this.layoutManager.isGrouped4FlyInActive = true;
      this.layoutManager.grouped4FlyInStartTime = Date.now();

      this.layoutManager.switchToLayout("grouped4", true);
    } else if (layoutType === "grid") {
      // Determine row count based on dataset
      let rowCount = 3; // Default for most datasets
      if (newDataset === photos5.value) {
        rowCount = 5; // Use 5 rows for photos5 dataset
      }

      this.layoutManager.setupGridLayout(rowCount);

      // Enhanced fly-in animation detection for main datasets and their subsets
      const isPhotos1OrSubset = newDataset === photos || this.isPhotos1Subset(newDataset);
      const isPhotos3OrSubset = newDataset === photos3.value || this.isPhotos3Subset(newDataset);
      const isPhotos5OrSubset = newDataset === photos5.value || this.isPhotos5Subset(newDataset);

      if (isPhotos1OrSubset || isPhotos3OrSubset || isPhotos5OrSubset) {
        let datasetName;
        let isSubset = false;

        if (isPhotos1OrSubset) {
          datasetName = "PHOTOS1";
          isSubset = newDataset !== photos;
        } else if (isPhotos3OrSubset) {
          datasetName = "PHOTOS3";
          isSubset = newDataset !== photos3.value;
        } else {
          datasetName = "PHOTOS5";
          isSubset = newDataset !== photos5.value;
        }

        this.layoutManager.setupGridFlyInAnimation();
        this.layoutManager.isGridFlyInActive = true;
        this.layoutManager.gridFlyInStartTime = Date.now();
      }

      this.layoutManager.switchToLayout("grid", true);
    } else if (layoutType === "random") {
      this.layoutManager.switchToLayout("random", false);
    }

    // Re-setup event listeners for new objects
    this.setupClickHandlers();

    // Update button states to show active dataset
    this.updateDatasetButtonStates(newDataset);
  }

  /**
   * Clear all current photo objects from the scene
   */
  clearCurrentPhotos() {
    // Remove objects from scene
    this.objects.forEach((object) => {
      if (object.element && object.element.parentNode) {
        object.element.parentNode.removeChild(object.element);
      }
      this.scene.remove(object);
    });

    // Clear objects array
    this.objects = [];

    // Destroy current layout manager
    if (this.layoutManager) {
      this.layoutManager.destroy();
      this.layoutManager = null;
    }
  }

  /**
   * Update button states to show active dataset
   * Note: SWIPER button is excluded from this system and uses independent highlighting
   */
  updateDatasetButtonStates(activeDataset) {
    const photos1Btn = document.getElementById("photos1");
    const photos2Btn = document.getElementById("photos2");
    const photos3Btn = document.getElementById("photos3");
    const photos4Btn = document.getElementById("photos4");
    const photos5Btn = document.getElementById("photos5");
    // Note: SWIPER button is intentionally excluded from dataset-based highlighting

    // Remove active class from all dataset buttons (excluding SWIPER)
    photos1Btn?.classList.remove("active-dataset");
    photos2Btn?.classList.remove("active-dataset");
    photos3Btn?.classList.remove("active-dataset");
    photos4Btn?.classList.remove("active-dataset");
    photos5Btn?.classList.remove("active-dataset");
    // Note: SWIPER button active-dataset class is not managed here

    // Add active class to current dataset button (excluding SWIPER)
    if (activeDataset === photos) {
      photos1Btn?.classList.add("active-dataset");
    } else if (activeDataset === photos2.value) {
      photos2Btn?.classList.add("active-dataset");
    } else if (activeDataset === photos3.value) {
      photos3Btn?.classList.add("active-dataset");
    } else if (activeDataset === photos4.value) {
      photos4Btn?.classList.add("active-dataset");
    } else if (activeDataset === photos5.value) {
      photos5Btn?.classList.add("active-dataset");
    }
    // Note: SWIPER button highlighting is managed independently via setSwiperButtonIndependentHighlight()
  }

  setupEventListeners() {
    const gridBtn = document.getElementById("grid");
    const flipGridBtn = document.getElementById("flipGrid");
    const randomBtn = document.getElementById("random");
    const random2Btn = document.getElementById("random2");
    const swiperBtn = document.getElementById("swiper");
    const photos5Btn = document.getElementById("photos5");
    const photos4Btn = document.getElementById("photos4");
    const photos3Btn = document.getElementById("photos3");
    const photos2Btn = document.getElementById("photos2");
    const photos1Btn = document.getElementById("photos1");

    gridBtn?.addEventListener("click", () => {
      stopScreensaverMode(); // Stop screensaver if active
      resetActiveSubButton(); // Reset sub-button navigation state
      this.switchToCSS3DMode();
      this.layoutManager.switchToLayout("grid", true);
    });

    flipGridBtn?.addEventListener("click", () => {
      stopScreensaverMode(); // Stop screensaver if active
      resetActiveSubButton(); // Reset sub-button navigation state
      this.switchToCSS3DMode();
      this.layoutManager.switchToLayout("flipGrid", true);
    });

    randomBtn?.addEventListener("click", () => {
      stopScreensaverMode(); // Stop screensaver if active
      resetActiveSubButton(); // Reset sub-button navigation state
      this.switchToCSS3DMode();
      this.layoutManager.switchToLayout("random", false);
    });

    random2Btn?.addEventListener("click", () => {
      stopScreensaverMode(); // Stop screensaver if active
      resetActiveSubButton(); // Reset sub-button navigation state
      this.switchToCSS3DMode();
      this.layoutManager.switchToLayout("random2", false);
    });

    swiperBtn?.addEventListener("click", () => {
      stopScreensaverMode(); // Stop screensaver if active
      resetActiveSubButton(); // Reset sub-button navigation state
      this.switchToSwiperMode();
    });

    photos5Btn?.addEventListener("click", () => {
      stopScreensaverMode(); // Stop screensaver if active
      resetActiveSubButton(); // Reset sub-button navigation state
      this.switchToCSS3DMode();
      this.switchToDataset(photos5.value, "grid", "photos5");
    });

    photos3Btn?.addEventListener("click", () => {
      stopScreensaverMode(); // Stop screensaver if active
      resetActiveSubButton(); // Reset sub-button navigation state
      this.switchToCSS3DMode();
      this.switchToDataset(photos3.value, "grid", "photos3");
    });

    photos4Btn?.addEventListener("click", () => {
      stopScreensaverMode(); // Stop screensaver if active
      resetActiveSubButton(); // Reset sub-button navigation state
      this.switchToCSS3DMode();
      this.switchToDataset(photos4.value, "grouped4", "photos4");
    });

    photos2Btn?.addEventListener("click", () => {
      stopScreensaverMode(); // Stop screensaver if active
      resetActiveSubButton(); // Reset sub-button navigation state
      this.switchToCSS3DMode();
      this.switchToDataset(photos2.value, "grouped", "photos2");
    });

    photos1Btn?.addEventListener("click", () => {
      stopScreensaverMode(); // Stop screensaver if active
      resetActiveSubButton(); // Reset sub-button navigation state
      this.switchToCSS3DMode();
      this.switchToDataset(photos.value, "grid");
    });

    // Add event listeners for PHOTOS1 sub-buttons
    photos1SubsetArrays.value.forEach((subset, index) => {
      const subBtnId = `photos1-sub-${index + 1}`;
      const subBtn = document.getElementById(subBtnId);

      subBtn?.addEventListener("click", () => {
        // Stop screensaver mode if active (same as original PHOTOS1 button)
        stopScreensaverMode();

        // Switch to CSS3D mode and apply the photo subset
        this.switchToCSS3DMode();
        this.switchToDataset(subset, "grid");
      });
    });

    // Add event listeners for PHOTOS2 sub-buttons
    photos2SubsetArrays.value.forEach((categorySubset, index) => {
      const subBtnId = `photos2-sub-${index + 1}`;
      const subBtn = document.getElementById(subBtnId);

      subBtn?.addEventListener("click", () => {
        // Stop screensaver mode if active (same as original PHOTOS2 button)
        stopScreensaverMode();

        // Switch to CSS3D mode and apply the category subset with grouped layout
        this.switchToCSS3DMode();
        this.switchToDataset(categorySubset, "grouped");
      });
    });

    // Add event listeners for PHOTOS3 sub-buttons
    photos3SubsetArrays.value.forEach((subset, index) => {
      const subBtnId = `photos3-sub-${index + 1}`;
      const subBtn = document.getElementById(subBtnId);

      subBtn?.addEventListener("click", () => {
        // Stop screensaver mode if active (same as original PHOTOS3 button)
        stopScreensaverMode();

        // Switch to CSS3D mode and apply the photo subset with grid layout
        this.switchToCSS3DMode();
        this.switchToDataset(subset, "grid");
      });
    });

    // Add event listeners for PHOTOS4 sub-buttons
    photos4SubsetArrays.value.forEach((categorySubset, index) => {
      const subBtnId = `photos4-sub-${index + 1}`;
      const subBtn = document.getElementById(subBtnId);

      subBtn?.addEventListener("click", () => {
        // Stop screensaver mode if active (same as original PHOTOS4 button)
        stopScreensaverMode();

        // Switch to CSS3D mode and apply the category subset with grouped4 layout
        this.switchToCSS3DMode();
        this.switchToDataset(categorySubset, "grouped4");
      });
    });

    // Add event listeners for PHOTOS5 sub-buttons
    photos5SubsetArrays.value.forEach((subset, index) => {
      const subBtnId = `photos5-sub-${index + 1}`;
      const subBtn = document.getElementById(subBtnId);

      subBtn?.addEventListener("click", () => {
        // Stop screensaver mode if active (same as original PHOTOS5 button)
        stopScreensaverMode();

        // Switch to CSS3D mode and apply the photo subset with grid layout
        this.switchToCSS3DMode();
        this.switchToDataset(subset, "grid");
      });
    });
  }

  /**
   * Setup click handlers for photo elements
   */
  setupClickHandlers() {
    // Click handlers are already set up in createPhotoElements method
    // This method is kept for compatibility with the switchToDataset method
  }

  animate() {
    requestAnimationFrame(() => this.animate());

    // Update animations through layout manager
    if (this.layoutManager) {
      this.layoutManager.updateAnimations();
    }

    this.render();
  }

  render() {
    this.renderer.render(this.scene, this.camera);
  }

  resize() {
    const container = document.querySelector("#SceneCon");
    const WD = container.clientWidth;
    const WH = container.clientHeight;

    this.camera.aspect = WD / WH;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(WD, WH);
    this.render();

    // Handle window resize through layout manager
    if (this.layoutManager) {
      this.layoutManager.onWindowResize();
    }
  }

  // Layout methods moved to LayoutManager

  /**
   * Switch to Swiper overlay mode (keep 3D scene active in background)
   */
  switchToSwiperMode() {
    // Pause PHOTOS3 grid scrolling animations when switching to Swiper mode
    if (this.layoutManager && this.layoutManager.isPhotos3Mode && this.layoutManager.currentLayout === "grid") {
      this.layoutManager.isFocusMode = true; // 暂停PHOTOS3网格滚动动画
    }
    swiperImgs.value = [];
    // get3dEffect({sid: 92, student_id: selectStudentId.value}).then((res) => {
    get3dEffect({ sid: 1, student_id: 3387 }).then((res) => {
      swiperImgs.value = res.data.data.map((item) => {
        return {
          id: item.id,
          src: item.file,
          title: item.title,
          description: item.nianji + item.banji + "&nbsp;&nbsp;" + item.student_name,
          zan: item.like,
          ax: item.praise,
        };
      });
      console.log("swiperImgs", swiperImgs.value);
    });

    // Keep CSS3D container visible and active
    // Show Swiper modal overlay
    isSwiperMode.value = true;
    console.log("isSwiperMode", isSwiperMode.value);

    // Set independent SWIPER button highlight (separate from dataset-based highlighting)
    setSwiperButtonIndependentHighlight(true);
  }

  /**
   * Switch to CSS3D mode (close Swiper overlay)
   */
  switchToCSS3DMode() {
    // Resume PHOTOS3 grid scrolling animations when returning from Swiper mode
    if (this.layoutManager && this.layoutManager.isPhotos3Mode && this.layoutManager.currentLayout === "grid") {
      this.layoutManager.isFocusMode = false; // 恢复PHOTOS3网格滚动动画
    }

    // Hide Swiper modal overlay
    isSwiperMode.value = false;

    // Clear independent SWIPER button highlight
    setSwiperButtonIndependentHighlight(false);

    // Update button states to reflect current CSS3D dataset (excluding SWIPER)
    this.updateDatasetButtonStates(this.currentDataset);

    // CSS3D container remains visible and active
  }

  // Swiper methods moved to SwiperGallery component

  destroy() {
    // Clean up screensaver timers
    stopScreensaverMode();

    // Clean up layout manager
    if (this.layoutManager) {
      this.layoutManager.destroy();
      this.layoutManager = null;
    }

    // Clear Vue reactive reference
    galleryInstance.value = null;

    // Clear instance
    CSS3DPhotoGallery.instance = null;
  }
}

onMounted(async () => {
  try {
    //屏保数据
    const res = await get3dEffects({ sid: 92 });
    photos.value = [];
    res.forEach((photo, index) => {
      photos.value.push({
        id: photo.id,
        src: photo.file,
        title: photo.title,
        nianji: photo.nianji,
        banji: photo.banji,
        student_name: photo.student_name,
        like: photo.like,
        praise: photo.praise,
        description: index,
      });
    });

    //获取年级信息
    await getGradeInfo({ sid: 92, campus_id: 105 });

    // //年级学生作品分组  主题模式
    await getStudentArtworkLists({ sid: 92, campus_id: 105 });
    new CSS3DPhotoGallery();

    // Automatically start screensaver mode after initialization
    // Wait a brief moment to ensure the gallery is fully initialized
    setTimeout(() => {
      // startScreensaverMode();
    }, 10);

    // Add keyboard navigation for sub-buttons
    window.addEventListener("keydown", handleSubButtonKeyNavigation);

    // Add mouse event listeners for desktop swipe simulation (optional)
    // This allows testing on desktop by dragging from top to bottom
    let mouseStartY = 0;
    let mouseStartTime = 0;
    let isMouseTracking = false;

    const handleMouseDown = (event) => {
      mouseStartY = event.clientY;
      mouseStartTime = Date.now();
      isMouseTracking = true;
    };

    const handleMouseUp = (event) => {
      if (!isMouseTracking) return;

      const endY = event.clientY;
      const endTime = Date.now();
      const deltaY = endY - mouseStartY;
      const deltaTime = endTime - mouseStartTime;

      isMouseTracking = false;

      // Check if this is a valid downward mouse drag (for desktop testing)
      const isValidDrag = deltaY > swipeGestureState.value.minSwipeDistance && deltaTime < swipeGestureState.value.maxSwipeTime && mouseStartY < 100;

      if (isValidDrag && !isHeadSelectVisible.value) {
        showHeadSelect();
      }
    };

    // Add desktop mouse event listeners
    document.addEventListener("mousedown", handleMouseDown);
    document.addEventListener("mouseup", handleMouseUp);

    // Store cleanup functions for onUnmounted
    window._headSelectMouseCleanup = () => {
      document.removeEventListener("mousedown", handleMouseDown);
      document.removeEventListener("mouseup", handleMouseUp);
    };

    // Start hint auto-hide timer
    startHintAutoHide();
  } catch (error) {
    console.error("Error initializing CSS3D Photo Gallery:", error);

    // Even if there's an error, still try to start screensaver mode with fallback data
    setTimeout(() => {
      startScreensaverMode();
    }, 10);
  }
});

// Cleanup keyboard event listener on unmount
onUnmounted(() => {
  window.removeEventListener("keydown", handleSubButtonKeyNavigation);

  // Cleanup mouse event listeners for headSelect
  if (window._headSelectMouseCleanup) {
    window._headSelectMouseCleanup();
  }

  // Clear any pending auto-hide timeout
  if (swipeGestureState.value.hideTimeout) {
    clearTimeout(swipeGestureState.value.hideTimeout);
  }

  // Clear any pending hint timeout
  if (swipeGestureState.value.hintTimeout) {
    clearTimeout(swipeGestureState.value.hintTimeout);
  }
});

// ----------------------------------------API--------------------------------------

//作品列表
const get3dEffects = async (params) => {
  const res = await get3dEffect(params);
  return res.data.data;
};
const grade_map = {
  一年级: 1,
  二年级: 2,
  三年级: 3,
  四年级: 4,
  五年级: 5,
};
//获取年级信息 年级模式使用
const getGradeInfo = async (params) => {
  photos2.value = [];
  //主题班级数据
  const studentCount = await getStudentCount(params);

  for (const key in studentCount.data.data) {
    let datacategory = {};
    datacategory.category = key;
    datacategory.title = key;

    const data = studentCount.data.data[key];
    //     data = {
    //     "nianji": "一年级",
    //     "title": "1",
    //     "njno": 1,
    //     "artwork_count": 90
    // }
    datacategory.photos = data;
    // console.log("data", data);
    // data.forEach((item) => {
    //   datacategory.photos.push(item);
    // });
    photos2.value.push(datacategory);
  }
  photos2.value.sort((a, b) => grade_map[a.category] - grade_map[b.category]);
};

const getByGradeInfo = async (params) => {
  // 返回 Promise 中的结果
  return new Promise(async (resolve, reject) => {
    try {
      photos5.value = [];

      // 调用异步函数获取数据
      const res = await get3dEffects({ sid: 92, student_id: "", banji: 68 });

      // 处理返回的数据
      res.forEach((item) => {
        let datacategory = {};
        datacategory.id = item.id;
        datacategory.src = item.file;
        datacategory.title = item.title;
        datacategory.description = item.description;
        photos5.value.push(datacategory);
      });

      // 通过 resolve 返回结果
      resolve(photos5.value);
    } catch (error) {
      // 捕获并处理错误
      console.error("Error fetching 3d effects:", error);
      reject(error);
    }
  });
};

//年级学生作品  主题模式使用 (text-only, following PHOTOS2 pattern)
const getStudentArtworkLists = async (params) => {
  photos4.value = [];
  const res = await getStudentArtworkList(params);
  for (const key in res.data.data) {
    let datacategory = {};
    datacategory.category = key;
    datacategory.title = key;
    datacategory.photos = [];
    const data = res.data.data[key];
    datacategory.photos = data;
    // data.forEach((item) => {
    //   let dataphoto = {};
    //   // dataphoto.id = item.id;
    //   // Remove src field for text-only PHOTOS4 mode
    //   dataphoto.title = item.title;

    //   dataphoto.description = item.description;
    //   datacategory.photos.push(dataphoto);
    // });
    photos4.value.push(datacategory);
    console.log("photos4", photos4.value);
  }
};
</script>

<style lang="less" scoped>
.App {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
  overflow: hidden;
  font-family: "Arial", sans-serif;
}

.SceneCon {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("../public/background.jfif") no-repeat center center;
  background-size: cover;
}

.menu {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 15px;
  z-index: 100;

  /* Ensure menu stays visible but below modal */
  &.modal-open {
    z-index: 999;
  }
}

/* Sub-menu container for photo dataset sub-buttons */
.sub-menu-container {
  position: absolute;
  bottom: 90px; /* Position above main menu */
  left: 50%;
  transform: translateX(-50%);
  z-index: 99;
  max-width: 90vw;
  max-height: 40vh; /* Limit height to prevent viewport overflow */

  /* Ensure sub-menu stays visible but below modal */
  &.modal-open {
    z-index: 998;
  }
}

/* Sub-menu navigation controls */
.sub-menu-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 10px;
  padding: 8px 15px;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  backdrop-filter: blur(8px);
}

.sub-nav-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.8);
  padding: 6px 12px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  border-radius: 15px;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  min-width: 40px;

  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    background: rgba(255, 255, 255, 0.3);
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
  }
}

.sub-nav-indicator {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  font-weight: bold;
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  min-width: 60px;
  text-align: center;
}

.sub-menu-content {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  align-items: flex-start;
  padding: 15px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);

  /* Enable scrolling if content exceeds container height */
  max-height: 35vh;
  overflow-y: auto;
  overflow-x: hidden;

  /* Custom scrollbar styling */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.screensaver-btn {
  // background: rgba(0, 0, 0, 0.8);
  // border: 2px solid rgba(255, 165, 0, 0.4); /* Orange accent for screensaver */
  // color: rgba(255, 165, 0, 0.9);
  padding: 2px 20px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  border-radius: 25px;
  transition: all 0.3s ease;
  // backdrop-filter: blur(15px);
  min-width: 120px;
  text-align: center;
  background-color: transparent;
  border: none;
  // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

  &:hover {
    background: rgba(255, 165, 0, 0.1);
    border-color: rgba(255, 165, 0, 0.7);
    color: rgba(255, 165, 0, 1);
    transform: translateY(-2px);
    box-shadow: 0 6px 18px rgba(255, 165, 0, 0.2);
  }

  &:active {
    transform: translateY(0);
    background: rgba(255, 165, 0, 0.2);
  }

  /* Active screensaver state */
  &.active {
    background: rgba(255, 165, 0, 0.2);
    border-color: rgba(255, 165, 0, 0.8);
    color: rgba(255, 165, 0, 1);
    box-shadow: 0 0 20px rgba(255, 165, 0, 0.4);
  }

  /* Countdown state with pulsing animation */
  &.countdown {
    animation: screensaver-pulse 1s ease-in-out infinite;
  }
}

/* Pulsing animation for countdown */
@keyframes screensaver-pulse {
  0% {
    box-shadow: 0 0 20px rgba(255, 165, 0, 0.4);
  }
  50% {
    box-shadow: 0 0 30px rgba(255, 165, 0, 0.7);
  }
  100% {
    box-shadow: 0 0 20px rgba(255, 165, 0, 0.4);
  }
}

/* Grade mode button styles */
.grade-mode-btn {
  // background: rgba(0, 0, 0, 0.8);
  // border: 2px solid rgba(100, 255, 150, 0.4); /* Green accent for grade mode */
  // color: rgba(100, 255, 150, 0.9);
  padding: 2px 20px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  border-radius: 25px;
  transition: all 0.3s ease;
  //  backdrop-filter: blur(15px);
  min-width: 120px;
  text-align: center;
  // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  background-color: transparent;
  border: none;

  &:hover {
    background: rgba(100, 255, 150, 0.1);
    border-color: rgba(100, 255, 150, 0.7);
    color: rgba(100, 255, 150, 1);
    transform: translateY(-2px);
    box-shadow: 0 6px 18px rgba(100, 255, 150, 0.2);
  }

  &:active {
    transform: translateY(0);
    background: rgba(100, 255, 150, 0.2);
  }

  /* Active grade mode state */
  &.active {
    background: rgba(100, 255, 150, 0.2);
    border-color: rgba(100, 255, 150, 0.8);
    color: rgba(255, 255, 255, 1);
    box-shadow: 0 0 20px rgba(100, 255, 150, 0.4);
    animation: grade-pulse 0.6s ease-out;
  }
}

/* Grade mode activation animation */
@keyframes grade-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(100, 255, 150, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(100, 255, 150, 0.7);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(100, 255, 150, 0.4);
  }
}

/* Theme mode button styles */
.theme-mode-btn {
  // background: rgba(0, 0, 0, 0.8);
  // border: 2px solid rgba(150, 100, 255, 0.4); /* Purple/violet accent for theme mode */
  // color: rgba(150, 100, 255, 0.9);
  padding: 2px 20px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  // border-radius: 25px;
  transition: all 0.3s ease;
  // backdrop-filter: blur(15px);
  min-width: 120px;
  text-align: center;
  background-color: transparent;
  border: none;
  // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

  // &:hover {
  //   background: rgba(150, 100, 255, 0.1);
  //   border-color: rgba(150, 100, 255, 0.7);
  //   color: rgba(150, 100, 255, 1);
  //   transform: translateY(-2px);
  //   box-shadow: 0 6px 18px rgba(150, 100, 255, 0.2);
  // }

  // &:active {
  //   transform: translateY(0);
  //   background: rgba(150, 100, 255, 0.2);
  // }

  // /* Active theme mode state */
  // &.active {
  //   background: rgba(150, 100, 255, 0.2);
  //   border-color: rgba(150, 100, 255, 0.8);
  //   color: rgba(255, 255, 255, 1);
  //   box-shadow: 0 0 20px rgba(150, 100, 255, 0.4);
  //   animation: theme-pulse 0.6s ease-out;
  // }
}

/* Theme mode activation animation */
@keyframes theme-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(150, 100, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(150, 100, 255, 0.7);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(150, 100, 255, 0.4);
  }
}

/* Focus mode navigation buttons */

.focus-nav-btn {
  background: rgba(0, 0, 0, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.9);
  padding: 12px 24px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  border-radius: 25px;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
  min-width: 100px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.6);
    color: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 6px 18px rgba(255, 255, 255, 0.2);
  }

  &:active {
    transform: translateY(0);
    background: rgba(255, 255, 255, 0.2);
  }
}

.focus-nav-btn.enter-btn {
  border-color: rgba(100, 150, 255, 0.5);
  color: rgba(100, 150, 255, 0.9);

  &:hover {
    border-color: rgba(100, 150, 255, 0.8);
    color: rgba(100, 150, 255, 1);
    box-shadow: 0 6px 18px rgba(100, 150, 255, 0.3);
  }
}

.focus-nav-btn.return-btn {
  border-color: rgba(255, 100, 100, 0.5);
  color: rgba(255, 100, 100, 0.9);

  &:hover {
    border-color: rgba(255, 100, 100, 0.8);
    color: rgba(255, 100, 100, 1);
    box-shadow: 0 6px 18px rgba(255, 100, 100, 0.3);
  }
}

.layout-btn {
  background: rgba(0, 0, 0, 0.7);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.8);
  padding: 12px 24px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  border-radius: 25px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.6);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  }

  &:active {
    transform: translateY(0);
    background: rgba(255, 255, 255, 0.2);
  }

  &.active-dataset {
    background: rgba(0, 150, 255, 0.3);
    border-color: rgba(0, 150, 255, 0.8);
    color: rgba(0, 150, 255, 1);
    box-shadow: 0 0 15px rgba(0, 150, 255, 0.4);
  }

  /* Independent SWIPER button highlight state (separate from dataset-based highlighting) */
  &.swiper-independent-highlight {
    background: rgba(255, 100, 0, 0.3) !important;
    border-color: rgba(255, 100, 0, 0.8) !important;
    color: rgba(255, 100, 0, 1) !important;
    box-shadow: 0 0 20px rgba(255, 100, 0, 0.5) !important;
    transform: translateY(-2px) scale(1.05) !important;
  }

  /* General sub-button styling for block-level display */
  &.sub-btn {
    display: block;
    width: auto;
    min-width: 200px;
    max-width: 300px;
    margin: 4px 0;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Active sub-button state (currently selected) */
  &.active-sub-button {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 20px rgba(255, 255, 255, 0.2);
    border-width: 3px;

    /* Add a subtle glow effect */
    &::before {
      content: "";
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: inherit;
      border-radius: inherit;
      opacity: 0.3;
      z-index: -1;
      filter: blur(4px);
    }
  }

  /* PHOTOS1 sub-button specific styling */
  &.photos1-sub-btn {
    background: rgba(50, 50, 50, 0.8);
    border: 2px solid rgba(100, 200, 100, 0.4);
    color: rgba(100, 200, 100, 0.9);
    font-size: 12px;
    padding: 8px 16px;

    &:hover {
      background: rgba(100, 200, 100, 0.15);
      border-color: rgba(100, 200, 100, 0.7);
      color: rgba(100, 200, 100, 1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(100, 200, 100, 0.3);
    }

    &:active {
      transform: translateY(0);
      background: rgba(100, 200, 100, 0.25);
    }

    &.active-dataset {
      background: rgba(100, 200, 100, 0.3);
      border-color: rgba(100, 200, 100, 0.8);
      color: rgba(100, 200, 100, 1);
      box-shadow: 0 0 15px rgba(100, 200, 100, 0.4);
    }
  }

  /* PHOTOS2 sub-button specific styling */
  &.photos2-sub-btn {
    background: rgba(50, 50, 80, 0.8);
    border: 2px solid rgba(100, 150, 255, 0.4);
    color: rgba(100, 150, 255, 0.9);
    font-size: 12px;
    padding: 8px 16px;

    &:hover {
      background: rgba(100, 150, 255, 0.15);
      border-color: rgba(100, 150, 255, 0.7);
      color: rgba(100, 150, 255, 1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(100, 150, 255, 0.3);
    }

    &:active {
      transform: translateY(0);
      background: rgba(100, 150, 255, 0.25);
    }

    &.active-dataset {
      background: rgba(100, 150, 255, 0.3);
      border-color: rgba(100, 150, 255, 0.8);
      color: rgba(100, 150, 255, 1);
      box-shadow: 0 0 15px rgba(100, 150, 255, 0.4);
    }
  }

  /* PHOTOS3 sub-button specific styling */
  &.photos3-sub-btn {
    background: rgba(80, 50, 50, 0.8);
    border: 2px solid rgba(255, 150, 100, 0.4);
    color: rgba(255, 150, 100, 0.9);
    font-size: 12px;
    padding: 8px 16px;

    &:hover {
      background: rgba(255, 150, 100, 0.15);
      border-color: rgba(255, 150, 100, 0.7);
      color: rgba(255, 150, 100, 1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 150, 100, 0.3);
    }

    &:active {
      transform: translateY(0);
      background: rgba(255, 150, 100, 0.25);
    }

    &.active-dataset {
      background: rgba(255, 150, 100, 0.3);
      border-color: rgba(255, 150, 100, 0.8);
      color: rgba(255, 150, 100, 1);
      box-shadow: 0 0 15px rgba(255, 150, 100, 0.4);
    }
  }

  /* PHOTOS4 sub-button specific styling */
  &.photos4-sub-btn {
    background: rgba(60, 40, 80, 0.8);
    border: 2px solid rgba(200, 100, 255, 0.4);
    color: rgba(200, 100, 255, 0.9);
    font-size: 12px;
    padding: 8px 16px;

    &:hover {
      background: rgba(200, 100, 255, 0.15);
      border-color: rgba(200, 100, 255, 0.7);
      color: rgba(200, 100, 255, 1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(200, 100, 255, 0.3);
    }

    &:active {
      transform: translateY(0);
      background: rgba(200, 100, 255, 0.25);
    }

    &.active-dataset {
      background: rgba(200, 100, 255, 0.3);
      border-color: rgba(200, 100, 255, 0.8);
      color: rgba(200, 100, 255, 1);
      box-shadow: 0 0 15px rgba(200, 100, 255, 0.4);
    }
  }

  /* PHOTOS5 sub-button specific styling */
  &.photos5-sub-btn {
    background: rgba(40, 80, 60, 0.8);
    border: 2px solid rgba(100, 255, 150, 0.4);
    color: rgba(100, 255, 150, 0.9);
    font-size: 12px;
    padding: 8px 16px;

    &:hover {
      background: rgba(100, 255, 150, 0.15);
      border-color: rgba(100, 255, 150, 0.7);
      color: rgba(100, 255, 150, 1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(100, 255, 150, 0.3);
    }

    &:active {
      transform: translateY(0);
      background: rgba(100, 255, 150, 0.25);
    }

    &.active-dataset {
      background: rgba(100, 255, 150, 0.3);
      border-color: rgba(100, 255, 150, 0.8);
      color: rgba(100, 255, 150, 1);
      box-shadow: 0 0 15px rgba(100, 255, 150, 0.4);
    }
  }
}
</style>

<style>
/* Global styles for CSS3D elements - PERFORMANCE OPTIMIZED */
.photo-frame {
  width: 300px;
  height: 400px;
  border-radius: 10px;
  cursor: pointer;
  /* PERFORMANCE: Simplified transitions - removed expensive properties */
  transition: background-color 0.3s ease, border-color 0.3s ease;
  overflow: hidden;
  /* PERFORMANCE: Removed backdrop-filter for better performance */
  background: rgba(0, 0, 0, 0.9);
  /* PERFORMANCE: Only set will-change when actually animating */
}

/* Layout-specific transition rules */
/* RANDOM layout: PERFORMANCE OPTIMIZED for 60 FPS - Minimal transitions */
.photo-frame.layout-random {
  /* PERFORMANCE: Only animate transform for hardware acceleration */
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* Enhanced hardware acceleration for smooth animations */
  transform-style: preserve-3d;
  backface-visibility: hidden;
  /* PERFORMANCE: will-change set dynamically via JavaScript for optimal layer management */
}

/* Waterfall rain layout: PERFORMANCE OPTIMIZED for 60 FPS - Faster transitions for more dynamic feel */
.photo-frame.layout-random2 {
  /* PERFORMANCE: Faster transitions for waterfall rain's more dynamic nature */
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* Enhanced hardware acceleration for smooth animations */
  transform-style: preserve-3d;
  backface-visibility: visible; /* Allow backface to be visible during animations */
  /* PERFORMANCE: will-change set dynamically via JavaScript for optimal layer management */
}

/* GROUPED layout: Enable smooth transitions for dataset switching */
.photo-frame.layout-grouped {
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* GROUPED layout fly-in animation: Disable transitions during fly-in for better control */
.photo-frame.layout-grouped.fly-in-active {
  transition: none !important;
}

/* GRID layout: Disable transform transitions to prevent animated wrap-around */
.photo-frame.layout-grid {
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
}

/* GRID layout fly-in animation: Disable transitions during fly-in for better control */
.photo-frame.layout-grid.fly-in-active {
  transition: none !important;
}

/* Layout switching: Smooth transitions when switching between layouts */
.photo-frame.layout-switching {
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Default hover effects - PERFORMANCE OPTIMIZED */
.photo-frame:hover {
  /* PERFORMANCE: Only use transform for hardware acceleration */
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
}

/* Enhanced hover effects for RANDOM layout - PERFORMANCE OPTIMIZED for 60 FPS */
.photo-frame.layout-random:hover {
  /* PERFORMANCE: Simplified hover effects using only transform and border */
  border-color: rgba(255, 255, 255, 0.8);
  border-width: 3px;
  /* Use transform instead of box-shadow for better performance */
  transform: scale(1.02);
}

/* Enhanced hover effects for waterfall rain layout - PERFORMANCE OPTIMIZED for 60 FPS */
.photo-frame.layout-random2:hover {
  /* PERFORMANCE: Distinct hover effects for waterfall rain with blue/cyan theme */
  border-color: rgba(100, 200, 255, 0.9);
  border-width: 3px;
  /* Slightly larger scale for more dynamic feel */
  transform: scale(1.03);
}

/* PERFORMANCE OPTIMIZED random layout elements for 60 FPS */
.photo-frame.layout-random {
  /* PERFORMANCE: Simplified styling - removed expensive box-shadow and backdrop-filter */
  border: 2px solid rgba(255, 255, 255, 0.3);
  /* Solid background instead of backdrop-filter for better performance */
  background: rgba(0, 0, 0, 0.9);
}

/* PERFORMANCE OPTIMIZED waterfall rain layout elements for 60 FPS */
.photo-frame.layout-random2 {
  /* PERFORMANCE: Distinct styling with blue/cyan theme for waterfall rain effect */
  border: 2px solid rgba(100, 200, 255, 0.4);
  /* Slightly different background for visual distinction */
  background: rgba(0, 10, 20, 0.9);
}

/* PERFORMANCE OPTIMIZED number overlay for random layout - 60 FPS target */
.photo-frame.layout-random .photo-number-overlay {
  /* PERFORMANCE: Removed backdrop-filter and box-shadow for better performance */
  background: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* PERFORMANCE OPTIMIZED number overlay for waterfall rain layout - 60 FPS target */
.photo-frame.layout-random2 .photo-number-overlay {
  /* PERFORMANCE: Distinct styling for waterfall rain with blue theme */
  background: rgba(0, 10, 20, 0.95);
  border: 1px solid rgba(100, 200, 255, 0.4);
}

/* Waterfall rain button distinct styling */
.random2-btn {
  background: linear-gradient(135deg, rgba(100, 200, 255, 0.2), rgba(50, 150, 200, 0.3)) !important;
  border: 2px solid rgba(100, 200, 255, 0.5) !important;
  color: rgba(150, 220, 255, 0.9) !important;
}

.random2-btn:hover {
  background: linear-gradient(135deg, rgba(100, 200, 255, 0.4), rgba(50, 150, 200, 0.5)) !important;
  border-color: rgba(100, 200, 255, 0.8) !important;
  color: rgba(200, 240, 255, 1) !important;
  transform: scale(1.05) !important;
}

/* FlipGrid button distinct styling */
.flipgrid-btn {
  background: linear-gradient(135deg, rgba(255, 150, 100, 0.2), rgba(200, 100, 150, 0.3)) !important;
  border: 2px solid rgba(255, 150, 100, 0.5) !important;
  color: rgba(255, 180, 120, 0.9) !important;
}

.flipgrid-btn:hover {
  background: linear-gradient(135deg, rgba(255, 150, 100, 0.4), rgba(200, 100, 150, 0.5)) !important;
  border-color: rgba(255, 150, 100, 0.8) !important;
  color: rgba(255, 200, 140, 1) !important;
  transform: scale(1.05) !important;
}

.photo-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

.photo-title {
}

.photo-description {
}

/* Numerical identifier overlay styles */
.photo-number-overlay {
  position: absolute;
  top: 8px;
  left: 8px;
  /* background: rgba(0, 0, 0, 0.8); */
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 50px;
  font-weight: bold;
  font-family: "Arial", sans-serif;
  z-index: 10;
  pointer-events: none; /* Don't interfere with hover effects */
  opacity: 0.9;
  /* OPTIMIZED: Faster transition for better performance during RANDOM layout animations */
  transition: opacity 0.15s ease;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Show number overlay more prominently on hover */
.photo-frame:hover .photo-number-overlay {
  opacity: 1;
  /* background: rgba(0, 0, 0, 0.9); */
}

/* Class to completely disable transitions during wrap-around for instant positioning */
.photo-frame.instant-position {
  transition: none !important;
}

.photo-frame.instant-position * {
  transition: none !important;
}

/* Focus mode styles - active in RANDOM and PHOTOS2 layouts - OPTIMIZED for 60 FPS */
.photo-frame.focus-fade-out {
  opacity: 0.1 !important;
  /* OPTIMIZED: Faster opacity transition for RANDOM layout performance */
  transition: opacity 0.2s ease !important;
  cursor: not-allowed !important; /* Show not-allowed cursor for non-focused elements */
}

.photo-frame.focus-centered {
  opacity: 1 !important;
  z-index: 1000; /* Bring focused element to front */
  /* OPTIMIZED: Faster opacity transition for RANDOM layout performance */
  transition: opacity 0.2s ease !important;
  box-shadow: 0 20px 60px rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(255, 255, 255, 0.8) !important;
  cursor: pointer !important; /* Keep pointer cursor for focused element */
}

/* ===== GROUPED TEMPLATE STYLES (PHOTOS 2 MODE) ===== */
.photo-frame-grouped {
  width: 300px;
  height: 250px;
  border-radius: 12px;
  cursor: pointer;
  position: relative;
  /* overflow: hidden; - REMOVED to eliminate overflow constraint */
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* Add containment for child elements */
  contain: layout style;
}

.photo-frame-grouped:hover .photo-category-badge {
  background: rgba(100, 150, 255, 0.9);
  transform: scale(1.1);
}

/* Category badge for grouped template */
.photo-category-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(100, 150, 255, 0.7);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  z-index: 15;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  /* Ensure badge stays within parent bounds */
  max-width: calc(100% - 24px); /* Account for left/right positioning */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Category-specific badge colors */
.photo-category-badge[data-category="nature"] {
  background: rgba(76, 175, 80, 0.8);
}

.photo-category-badge[data-category="architecture"] {
  background: rgba(255, 152, 0, 0.8);
}

.photo-category-badge[data-category="abstract"] {
  background: rgba(156, 39, 176, 0.8);
}

/* PHOTOS4 category-specific badge colors */
.photo-category-badge[data-category="wildlife"] {
  background: rgba(139, 69, 19, 0.8); /* Brown for wildlife */
}

.photo-category-badge[data-category="space"] {
  background: rgba(25, 25, 112, 0.8); /* Dark blue for space */
}

.photo-category-badge[data-category="cuisine"] {
  background: rgba(255, 69, 0, 0.8); /* Orange-red for cuisine */
}

.photo-category-badge[data-category="sports"] {
  background: rgba(34, 139, 34, 0.8); /* Forest green for sports */
}

/* Number overlay for grouped template */
.photo-number-overlay-grouped {
  position: absolute;
  top: 12px;
  left: 12px;
  /* background: rgba(0, 0, 0, 0.85); */
  color: white;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: bold;
  font-family: "Arial", sans-serif;
  z-index: 15;
  pointer-events: none;
  opacity: 0.9;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  /* Ensure overlay stays within parent bounds */
  max-width: calc(100% - 24px); /* Account for left/right positioning */
  overflow: hidden;
  text-overflow: ellipsis;
}

.photo-frame-grouped:hover .photo-number-overlay-grouped {
  opacity: 1;
  /* background: rgba(0, 0, 0, 0.95); */
  transform: scale(1.05);
}

/* Image styles removed for text-only PHOTOS4 mode */

/* Content container for grouped template */

/* Title for grouped template */
.photo-title-grouped {
  font-size: 16px;
  font-weight: bold;
  color: white;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
  margin-bottom: 8px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Description for grouped template */
.photo-description-grouped {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Focus mode styles for grouped template */
.photo-frame-grouped.focus-fade-out {
  opacity: 0.1 !important;
  transition: opacity 0.4s ease !important;
  cursor: not-allowed !important; /* Show not-allowed cursor for non-focused elements */
}

.photo-frame-grouped.focus-centered {
  opacity: 1 !important;
  z-index: 1000;
  transition: opacity 0.4s ease !important;
  border-color: rgba(100, 150, 255, 0.9) !important;
  cursor: pointer !important; /* Keep pointer cursor for focused element */
}

/* Focus navigation buttons within grouped photo frame */
.photo-frame-grouped .focus-nav-buttons {
  position: absolute;
  bottom: -64px;
  left: 50%;
  transform: translateX(-50%);
  display: none; /* Hidden by default */
  flex-direction: row;
  gap: 15px;
  z-index: 1001;
  pointer-events: auto;
  /* Ensure buttons stay within parent bounds */
  max-width: calc(100% - 30px); /* Account for left/right margins */
  justify-content: center;
}

/* Show buttons only when element is focused */
.photo-frame-grouped.focus-centered .focus-nav-buttons {
  display: flex;
}

/* Instant positioning for grouped template */
.photo-frame-grouped.instant-position {
  transition: none !important;
}

.photo-frame-grouped.instant-position * {
  transition: none !important;
}

/* Fly-in animation for grouped template */
.photo-frame-grouped.fly-in-active {
  transition: none !important;
}

/* ===== PHOTOS 5 TEMPLATE STYLES ===== */
/* Dedicated styles for PHOTOS 5 5-row grid layout */

/* PERFORMANCE OPTIMIZED PHOTOS5 template for 60 FPS */
.photo-frame-photos5 {
  width: 300px;
  height: 254px;
  background: rgba(0, 0, 0, 0.9);
  border: 2px solid rgba(100, 255, 150, 0.3); /* Green accent for PHOTOS 5 */
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  /* PERFORMANCE: Simplified transitions - only transform */
  transition: transform 0.4s ease;
  /* PERFORMANCE: Removed expensive box-shadow and backdrop-filter */
}

/* PERFORMANCE OPTIMIZED hover effects for PHOTOS5 */
.photo-frame-photos5:hover {
  /* PERFORMANCE: Only use transform and border for hardware acceleration */
  border-color: rgba(100, 255, 150, 0.7);
  transform: translateY(-5px) scale(1.02);
}

/* Number overlay for PHOTOS 5 template */
.photo-number-overlay-photos5 {
  position: absolute;
  top: 12px;
  left: 12px;
  background: rgba(0, 0, 0, 0.85);
  color: rgba(100, 255, 150, 1);
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: bold;
  font-family: "Arial", sans-serif;
  z-index: 15;
  pointer-events: none;
  opacity: 0.9;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(100, 255, 150, 0.3);
}

/* Row indicator for PHOTOS 5 template */
.photo-row-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(100, 255, 150, 0.8);
  color: rgba(0, 0, 0, 0.9);
  padding: 4px 8px;
  border-radius: 50%;
  font-size: 10px;
  font-weight: bold;
  font-family: "Arial", sans-serif;
  z-index: 15;
  pointer-events: none;
  opacity: 0.9;
  transition: all 0.3s ease;
  min-width: 20px;
  text-align: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

/* Grid position indicator for PHOTOS 5 template */
.photo-grid-position {
  position: absolute;
  bottom: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.7);
  color: rgba(100, 255, 150, 0.8);
  padding: 3px 6px;
  border-radius: 4px;
  font-size: 9px;
  font-weight: bold;
  font-family: "Arial", sans-serif;
  z-index: 15;
  pointer-events: none;
  opacity: 0.7;
  transition: all 0.3s ease;
  border: 1px solid rgba(100, 255, 150, 0.2);
}

/* Image for PHOTOS 5 template */
.photo-image-photos5 {
  width: 100%;
  height: 280px;
  object-fit: cover;
  border-radius: 10px 10px 0 0;
  transition: transform 0.3s ease;
}

.photo-frame-photos5:hover .photo-image-photos5 {
  transform: scale(1.02);
}

/* Content container for PHOTOS 5 template */
.photo-content-photos5 {
  padding: 16px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%);
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Title for PHOTOS 5 template */
.photo-title-photos5 {
  font-size: 16px;
  font-weight: bold;
  color: rgba(100, 255, 150, 1);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
  margin-bottom: 8px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Description for PHOTOS 5 template */
.photo-description-photos5 {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Hover effects for PHOTOS 5 template */
.photo-frame-photos5:hover .photo-number-overlay-photos5,
.photo-frame-photos5:hover .photo-row-indicator,
.photo-frame-photos5:hover .photo-grid-position {
  opacity: 1;
  transform: scale(1.05);
}

.photo-frame-photos5:hover .photo-number-overlay-photos5 {
  background: rgba(0, 0, 0, 0.95);
  border-color: rgba(100, 255, 150, 0.5);
}

/* Focus mode styles for PHOTOS 5 template */
.photo-frame-photos5.focus-fade-out {
  opacity: 0.1 !important;
  transition: opacity 0.4s ease !important;
  cursor: not-allowed !important;
}

.photo-frame-photos5.focus-centered {
  opacity: 1 !important;
  z-index: 1000;
  transition: opacity 0.4s ease !important;
  box-shadow: 0 25px 70px rgba(100, 255, 150, 0.4) !important;
  border-color: rgba(100, 255, 150, 0.9) !important;
  cursor: pointer !important;
}

/* Instant positioning for PHOTOS 5 template */
.photo-frame-photos5.instant-position {
  transition: none !important;
}

.photo-frame-photos5.instant-position * {
  transition: none !important;
}

/* Fly-in animation for PHOTOS 5 template */
.photo-frame-photos5.fly-in-active {
  transition: none !important;
}

.headSelect {
  width: 827px;
  height: 233px;
  /* width: 539px; */
  padding: 12px 37px;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translate(-50%, -100%); /* Hide by default - moved up by 100% */
  background-image: url("../public/h.png");
  background-repeat: no-repeat;
  background-size: contain;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* Smooth slide animation */
  z-index: 1000; /* Ensure it appears above other content */
}

/* Visible state for headSelect */
.headSelect.visible {
  transform: translate(-50%, 0); /* Slide down to normal position */
}

/* Enhanced animation for smooth appearance */
.headSelect.slide-down {
  animation: slideDownFromTop 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes slideDownFromTop {
  from {
    transform: translate(-50%, -100%);
    opacity: 0.8;
  }
  to {
    transform: translate(-50%, 0);
    opacity: 1;
  }
}

/* Add a subtle backdrop when headSelect is visible */
/* .headSelect.visible::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: -1;
  pointer-events: none;
  opacity: 0;
  animation: fadeInBackdrop 0.4s ease forwards;
} */

@keyframes fadeInBackdrop {
  to {
    opacity: 1;
  }
}

/* Add hover effect for better user feedback */
.headSelect.visible {
  cursor: pointer;
}

/* .headSelect.visible:hover {
  transform: translate(-50%, -5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
} */

/* Swipe hint indicator styles */
.swipe-hint {
  position: fixed;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  pointer-events: none;
  transition: opacity 0.3s ease, transform 0.3s ease;
  animation: pulseHint 2s infinite;
}

.swipe-hint.hidden {
  opacity: 0;
  transform: translateX(-50%) translateY(-20px);
}

.swipe-hint-icon {
  font-size: 20px;
  margin-bottom: 4px;
  animation: bounceDown 1.5s infinite;
}

.swipe-hint-text {
  font-size: 10px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  white-space: nowrap;
}

@keyframes pulseHint {
  0%,
  100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

@keyframes bounceDown {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(3px);
  }
  60% {
    transform: translateY(1px);
  }
}
.headSelect1 {
  /* height: 35%; */
  margin-top: -25px;
  width: 91px;
  background-color: transparent; /* Remove background to let buttons show through */
  /* margin-left: 30px; */
  display: flex;
  align-items: center;
  justify-content: center;

  /* border-radius: 248px; */
  overflow: hidden;
}

.headSelect1:first-child {
  /* margin-left: 83px; */
}

/* Adjust button sizes within headSelect1 containers */
.headSelect1 .screensaver-btn,
.headSelect1 .grade-mode-btn,
.headSelect1 .theme-mode-btn {
  padding: 8px 12px;
  font-size: 12px;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.headSelect1 {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 70px;
}

.icon1,
.icon2,
.icon3,
.icon4 {
  width: 60px;
  height: 60px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  /* margin-bottom: 16px; */
}
.icon1 {
  background-image: url("/icon_zt.png");
}
.icon2 {
  background-image: url("/icon_bj.png");
}
.icon3 {
  background-image: url("/icon_hd.png");
}
.icon4 {
  background-image: url("/icon_pb.png");
}
</style>
