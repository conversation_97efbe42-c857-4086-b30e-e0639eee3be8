<template>
  <!-- Grouped Template Component - Hidden by default, used for cloning -->
  <div id="photo-frame-template-grouped" class="photo-frame-grouped" style="display: none" ref="templateRef">
    <!-- Text content area replacing the image -->
    <div class="photo-text-content-grouped">
      <div class="photo-text-content-grouped1">班级名称</div>
      <div class="photo-text-content-grouped2 classname">一年级2班</div>
      <div class="photo-text-content-grouped3">
        <div class="photo-text-content-grouped31">学生：</div>
        <div class="photo-text-content-grouped32 student">3114名</div>
      </div>
    </div>

    <!-- Focus mode navigation buttons (hidden by default) -->
    <div class="focus-nav-buttons">
      <button class="focus-nav-btn enter-btn">进入</button>
      <button class="focus-nav-btn return-btn">返回</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineExpose } from "vue";

// Template reference for external access
const templateRef = ref(null);

// Props for component configuration
const props = defineProps({
  // Allow customization of template ID if needed
  templateId: {
    type: String,
    default: "photo-frame-template-grouped",
  },
  // Allow visibility control from parent
  visible: {
    type: Boolean,
    default: false,
  },
});

// Update template ID and visibility when component mounts
onMounted(() => {
  if (templateRef.value) {
    // Set custom template ID if provided
    if (props.templateId !== "photo-frame-template-grouped") {
      templateRef.value.id = props.templateId;
    }

    // Update visibility based on props
    if (props.visible) {
      templateRef.value.style.display = "";
    }
  } else {
    console.warn("⚠️ Template reference not available in PhotoFrameGroupedTemplate");
  }
});

// Expose template reference and utility methods for parent access
defineExpose({
  templateRef,
  getTemplateElement: () => templateRef.value,
  setVisible: (visible) => {
    if (templateRef.value) {
      templateRef.value.style.display = visible ? "" : "none";
    }
  },
  getId: () => templateRef.value?.id || props.templateId,
});
</script>

<style scoped>
/* ===== GROUPED TEMPLATE STYLES (PHOTOS 2 MODE) ===== */
.photo-frame-grouped {
  width: 387px;
  height: 250px;
  border-radius: 12px;
  cursor: pointer;
  position: relative;
  will-change: transform;

  background-image: url("../../public/q1.png");
  background-repeat: no-repeat;
  background-size: contain;
}

/* Category badge for grouped template */
.photo-category-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(100, 150, 255, 0.7);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  z-index: 15;
  transition: all 0.3s ease;
  /* Ensure badge stays within parent bounds */
  max-width: calc(100% - 24px); /* Account for left/right positioning */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Category-specific badge colors */
.photo-category-badge[data-category="nature"] {
  background: rgba(76, 175, 80, 0.8);
}

.photo-category-badge[data-category="architecture"] {
  background: rgba(255, 152, 0, 0.8);
}

.photo-category-badge[data-category="abstract"] {
  background: rgba(156, 39, 176, 0.8);
}

/* PHOTOS4 category-specific badge colors */
.photo-category-badge[data-category="wildlife"] {
  background: rgba(139, 69, 19, 0.8); /* Brown for wildlife */
}

.photo-category-badge[data-category="space"] {
  background: rgba(25, 25, 112, 0.8); /* Dark blue for space */
}

.photo-category-badge[data-category="cuisine"] {
  background: rgba(255, 69, 0, 0.8); /* Orange-red for cuisine */
}

.photo-category-badge[data-category="sports"] {
  background: rgba(34, 139, 34, 0.8); /* Forest green for sports */
}

/* Number overlay for grouped template */
.photo-number-overlay-grouped {
  position: absolute;
  top: 12px;
  left: 12px;
  color: white;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: bold;
  font-family: "Arial", sans-serif;
  z-index: 15;
  pointer-events: none;
  opacity: 0.9;
  transition: all 0.3s ease;
  white-space: nowrap;
  /* Ensure overlay stays within parent bounds */
  max-width: calc(100% - 24px); /* Account for left/right positioning */
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Text content area for grouped template */
.photo-text-content-grouped {
  width: 100%;
  height: 100%;
}
.photo-text-content-grouped1 {
  width: 100%;
  height: 74px;
  margin-top: 19px;
  color: #734c03;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30px;
  letter-spacing: 2px;
  font-weight: bold;
}
.photo-text-content-grouped2 {
  width: 100%;
  margin-top: 13px;
  height: 50px;
  color: #000000;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 25px;
  letter-spacing: 5px;
}
.photo-text-content-grouped3 {
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 23px;
}
.photo-text-content-grouped31 {
  color: #734c03;
}
.photo-text-content-grouped32 {
  color: #000000;
}

.photo-text-main-grouped {
  position: absolute;
  width: 100%;
  left: 0;
  top: 27px;
  height: 46px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 19px;
  letter-spacing: 2px;
  color: #333;
}

.photo-text-details-grouped {
  position: absolute;
  width: 100%;
  left: 0;
  top: 81px;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 19px;
  letter-spacing: 2px;
}

.photo-text-class-grouped {
  color: #753d00;
  margin-right: 10px;
}

.photo-text-name-grouped {
  color: #333;
  margin-right: 15px;
}

.photo-text-stats-grouped {
  display: flex;
  gap: 10px;
}

.photo-text-stat-item-grouped {
  display: flex;
  align-items: center;
  gap: 3px;
}

.photo-text-stat-icon-grouped {
  width: 16px;
  height: 16px;
  background-image: url("../../public/icon1.png");
  background-repeat: no-repeat;
  background-size: contain;
}

.photo-text-stat-icon2-grouped {
  background-image: url("../../public/icon2.png");
}

.photo-text-stat-value-grouped {
  color: #333;
  font-size: 16px;
}

/* Note: Content container and title/description styles removed as we now use text content area */

/* Focus mode styles for grouped template */
.photo-frame-grouped.focus-fade-out {
  opacity: 0.1 !important;
  transition: opacity 0.4s ease !important;
  cursor: not-allowed !important; /* Show not-allowed cursor for non-focused elements */
}

.photo-frame-grouped.focus-centered {
  opacity: 1 !important;
  z-index: 1000;
  transition: opacity 0.4s ease !important;
  cursor: pointer !important; /* Keep pointer cursor for focused element */
}

/* Focus navigation buttons within grouped photo frame */
.photo-frame-grouped .focus-nav-buttons {
  position: absolute;
  bottom: -64px;
  left: 50%;
  transform: translateX(-50%);
  display: none; /* Hidden by default */
  flex-direction: row;
  gap: 15px;
  z-index: 1001;
  pointer-events: auto;
  /* Ensure buttons stay within parent bounds */
  max-width: calc(100% - 30px); /* Account for left/right margins */
  justify-content: center;
}

/* Show buttons only when element is focused */
.photo-frame-grouped.focus-centered .focus-nav-buttons {
  display: flex;
}

/* Focus navigation button styles */
.focus-nav-btn {
  background: linear-gradient(135deg, rgba(100, 150, 255, 0.9) 0%, rgba(80, 120, 200, 0.9) 100%);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  min-width: 60px;
  text-align: center;
  user-select: none;
  /* Ensure buttons are clickable */
  pointer-events: auto;
  z-index: 1002;
}

.focus-nav-btn:hover {
  background: linear-gradient(135deg, rgba(120, 170, 255, 1) 0%, rgba(100, 140, 220, 1) 100%);
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 6px 20px rgba(100, 150, 255, 0.4);
  transform: translateY(-2px);
}

.focus-nav-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.4);
}

/* Specific styles for enter and return buttons */
.focus-nav-btn.enter-btn {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.9) 0%, rgba(56, 142, 60, 0.9) 100%);
}

.focus-nav-btn.enter-btn:hover {
  background: linear-gradient(135deg, rgba(102, 187, 106, 1) 0%, rgba(76, 175, 80, 1) 100%);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.focus-nav-btn.return-btn {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.9) 0%, rgba(245, 124, 0, 0.9) 100%);
}

.focus-nav-btn.return-btn:hover {
  background: linear-gradient(135deg, rgba(255, 183, 77, 1) 0%, rgba(255, 152, 0, 1) 100%);
  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
}

/* Instant positioning for grouped template */
.photo-frame-grouped.instant-position {
  transition: none !important;
}

.photo-frame-grouped.instant-position * {
  transition: none !important;
}

/* Fly-in animation for grouped template */
.photo-frame-grouped.fly-in-active {
  transition: none !important;
}
</style>
