<template>
  <!-- photos5 Template Component - Hidden by default, used for cloning -->
  <div id="photo-frame-template-photos5" class="photo-frame-photos5" style="display: none" ref="templateRef">
    <img class="photo-image-photos5" alt="" />

    <div class="photo-frame-photos5Chi">
      <div class="photo-frame-photos5ChiText1">5画植物主题支持十一字</div>
      <div class="photo-frame-photos5ChiText2">
        <div class="photo-frame-photos5ChiText21">五年级2班</div>
        <div class="photo-frame-photos5ChiText22">张凌萱</div>

        <div class="photo-frame-photos5ChiText23">
          <div class="photos5ChiText231 likeDom">
            <div class="photos5ChiText231Icon"></div>
            <div class="photos5ChiText231Text likeDomvalue">55</div>
          </div>
          <div class="photos5ChiText231 photos5ChiText2312 praiseDom">
            <div class="photos5ChiText231Icon photos5ChiText231Icon1"></div>
            <div class="photos5ChiText231Text photos5ChiText231Text123">21</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineExpose } from "vue";

// Template reference for external access
const templateRef = ref(null);

// Props for component configuration
const props = defineProps({
  // Allow customization of template ID if needed
  templateId: {
    type: String,
    default: "photo-frame-template-photos5",
  },
  // Allow visibility control from parent
  visible: {
    type: Boolean,
    default: false,
  },
});

// Update template ID and visibility when component mounts
onMounted(() => {
  if (templateRef.value) {
    // Set custom template ID if provided
    if (props.templateId !== "photo-frame-template-photos5") {
      templateRef.value.id = props.templateId;
    }

    // Update visibility based on props
    if (props.visible) {
      templateRef.value.style.display = "";
    }
  } else {
  }
});

// Expose template reference and utility methods for parent access
defineExpose({
  templateRef,
  getTemplateElement: () => templateRef.value,
  setVisible: (visible) => {
    if (templateRef.value) {
      templateRef.value.style.display = visible ? "" : "none";
    }
  },
  getId: () => templateRef.value?.id || props.templateId,
});
</script>

<style scoped>
/* ===== PHOTOS 1 TEMPLATE STYLES ===== */
.photo-frame-photos5 {
  width: 500px;
  height: 300px;
  padding: 15px;
  /* background: rgba(0, 0, 0, 0.9); */
  /* border: 2px solid rgba(100, 150, 255, 0.3); */
  /* box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5); */
  cursor: pointer;
  /* Default: smooth transitions for hover effects only */
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
  /* overflow: hidden; */
  /* Optimize for 3D transforms - enables hardware acceleration */
  will-change: transform;

  background-image: url("../../public/a.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;

  /* background-color: none !important; */

  display: flex;
  justify-content: center;
  align-items: center;

  /* backdrop-filter: blur(10px); */
}

/* Layout-specific transition rules for photos5 - PERFORMANCE OPTIMIZED for 60 FPS */
.photo-frame-photos5.layout-random {
  /* PERFORMANCE: Only animate transform for hardware acceleration */
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* Enhanced hardware acceleration for smooth animations */
  transform-style: preserve-3d;
  backface-visibility: hidden;
  /* PERFORMANCE: will-change managed dynamically via JavaScript */
}

/* Layout-specific transition rules for photos5 RANDOM2 - PERFORMANCE OPTIMIZED for 60 FPS */
.photo-frame-photos5.layout-random2 {
  /* PERFORMANCE: Faster transitions for RANDOM2's more dynamic nature */
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* Enhanced hardware acceleration for smooth animations */
  transform-style: preserve-3d;
  backface-visibility: visible; /* FIXED: Allow backface to be visible during Y-axis rotation */
  /* PERFORMANCE: will-change managed dynamically via JavaScript */
}

.photo-frame-photos5.layout-grid {
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
}

.photo-frame-photos5.layout-grid.fly-in-active {
  transition: none !important;
}

.photo-frame-photos5.layout-switching {
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* .photo-frame-photos5:hover {
  border-color: rgba(100, 150, 255, 0.6);
  box-shadow: 0 15px 40px rgba(100, 150, 255, 0.3);
  transform: scale(1.05);
}

.photo-frame-photos5.layout-random:hover {
  border-color: rgba(100, 150, 255, 0.8);
  background-color: rgba(0, 127, 127, 0.6) !important;
  transform: scale(1.02);
} */

/* .photo-frame-photos5.layout-random2:hover {
  border-color: rgba(255, 100, 200, 0.9);
  background-color: rgba(50, 0, 25, 0.7) !important;
  transform: scale(1.03);
} */

.photo-frame-photos5.instant-position {
  transition: none !important;
}

.photo-frame-photos5.instant-position * {
  transition: none !important;
}

/* Focus mode styles - active in RANDOM, RANDOM2, GRID (photos5) and PHOTOS2 layouts */
.photo-frame-photos5.focus-fade-out {
  opacity: 0.1 !important;
  transition: opacity 0.4s ease !important;
  cursor: not-allowed !important; /* Show not-allowed cursor for non-focused elements */
}

.photo-frame-photos5.focus-centered {
  opacity: 1 !important;
  z-index: 1000; /* Bring focused element to front */
  transition: opacity 0.4s ease !important;
  box-shadow: 0 20px 60px rgba(100, 150, 255, 0.3) !important;
  border-color: rgba(100, 150, 255, 0.8) !important;
  cursor: pointer !important; /* Keep pointer cursor for focused element */
}

/* Child element styles */
.photo-frame-photos5 .photo-number-overlay {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(100, 150, 255, 0.8);
  color: white;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: bold;
  z-index: 10;
}

.photo-frame-photos5 .photo-image {
  width: 73%;
  height: 86%;
  /* height: 70%; */
  object-fit: cover;
  border-radius: none !important;
}

.photo-frame-photos5 .photo-title {
  position: absolute;
  bottom: 60px;
  left: 15px;
  right: 15px;
  color: white;
  font-size: 16px;
  font-weight: bold;
  /* text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8); */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.photo-frame-photos5 .photo-description {
  position: absolute;
  bottom: 15px;
  left: 15px;
  right: 15px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  /* text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8); */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.photo-frame-photos5Chi {
  position: absolute;
  width: 73%;
  height: 137px;
  top: 325px;
  left: 50%;
  transform: translate(-50%, 0%);
  background-image: url("../../public/a1.png");
  background-repeat: no-repeat;
  background-size: contain;
  /* Hide Chinese text overlay by default */
  opacity: 0;
  visibility: hidden;
  /* Smooth transition for show/hide effect */
  transition: opacity 0.4s ease, visibility 0.4s ease, transform 0.4s ease;
  /* Slightly scale down when hidden for better animation effect */
  transform: translate(-50%, 0%) scale(0.95);
}

/* Show Chinese text overlay when parent photo frame is focused */
.photo-frame-photos5.focus-centered .photo-frame-photos5Chi {
  opacity: 1;
  visibility: visible;
  /* Restore normal scale when visible */
  transform: translate(-50%, 0%) scale(1);
}
.photo-frame-photos5ChiText1 {
  position: absolute;
  width: 100%;
  left: 0;
  top: 27px;
  height: 46px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 19px;
  letter-spacing: 2px;
}
.photo-frame-photos5ChiText2 {
  position: absolute;
  width: 100%;
  left: 0;
  top: 81px;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 19px;
  letter-spacing: 2px;
}
.photo-frame-photos5ChiText21 {
  color: #753d00;
}
.photo-frame-photos5ChiText22 {
  margin-left: 10px;
}
.photo-frame-photos5ChiText23 {
  position: absolute;
  width: 100%;
  left: 0;
  top: 55px;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 19px;
  letter-spacing: 2px;
}
.photos5ChiText231 {
  width: 88px;
  height: 100%;
  background-image: url("../../public/a4.png");
  background-repeat: no-repeat;
  background-size: contain;

  display: flex;
  justify-content: center;
  align-items: center;
}
.photos5ChiText231Active {
  background-image: url("../../public/a41.png");
}
.photos5ChiText231Icon {
  width: 20px;
  height: 20px;
  background-image: url("../../public/a2.png");
  background-repeat: no-repeat;
  background-size: contain;
}
.photos5ChiText231IconActive {
  background-image: url("../../public/a21.png");
}
.photos5ChiText231Text {
  margin-left: 5px;
  color: #c65134;
}

.photos5ChiText231Icon1 {
  background-image: url("../../public/a3.png");
}
.photos5ChiText231Icon1Active {
  background-image: url("../../public/a31.png");
}
.photos5ChiText2312 {
  margin-left: 20px;
}

.photo-image-photos5 {
  object-fit: cover;
  width: 100% !important;
  height: 100% !important;
}
</style>
