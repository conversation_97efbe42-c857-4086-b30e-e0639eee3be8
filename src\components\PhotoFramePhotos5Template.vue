<template>
  <!-- PHOTOS5 Template Component - Hidden by default, used for cloning -->
  <div id="photo-frame-template-photos5" class="photo-frame-photos5" style="display: none" ref="templateRef">
    <div class="photo-number-overlay-photos5"></div>
    <div class="photo-row-indicator"></div>
    <img class="photo-image-photos5" alt="" />
    <div class="photo-content-photos5">
      <div class="photo-title-photos5"></div>
      <div class="photo-description-photos5"></div>
    </div>
    <div class="photo-grid-position"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineExpose } from "vue";

// Template reference for external access
const templateRef = ref(null);

// Props for component configuration
const props = defineProps({
  // Allow customization of template ID if needed
  templateId: {
    type: String,
    default: "photo-frame-template-photos5",
  },
  // Allow visibility control from parent
  visible: {
    type: Boolean,
    default: false,
  },
});

// Update template ID and visibility when component mounts
onMounted(() => {
  if (templateRef.value) {
    // Set custom template ID if provided
    if (props.templateId !== "photo-frame-template-photos5") {
      templateRef.value.id = props.templateId;
    }

    // Update visibility based on props
    if (props.visible) {
      templateRef.value.style.display = "";
    }
  } else {
  }
});

// Expose template reference and utility methods for parent access
defineExpose({
  templateRef,
  getTemplateElement: () => templateRef.value,
  setVisible: (visible) => {
    if (templateRef.value) {
      templateRef.value.style.display = visible ? "" : "none";
    }
  },
  getId: () => templateRef.value?.id || props.templateId,
});
</script>

<style scoped>
/* Dedicated styles for PHOTOS 5 5-row grid layout */

.photo-frame-photos5 {
  width: 300px;
  height: 254px;
  background: rgba(0, 0, 0, 0.9);
  border: 2px solid rgba(100, 255, 150, 0.3); /* Green accent for PHOTOS 5 */
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  /* Optimize for 3D transforms - enables hardware acceleration */
  will-change: transform;
}

.photo-frame-photos5:hover {
  border-color: rgba(100, 255, 150, 0.7);
  box-shadow: 0 15px 40px rgba(100, 255, 150, 0.2);
  transform: translateY(-5px);
}

/* Number overlay for PHOTOS 5 template */
.photo-number-overlay-photos5 {
  position: absolute;
  top: 12px;
  left: 12px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: bold;
  z-index: 15;
  border: 1px solid rgba(100, 255, 150, 0.3);
  opacity: 0.9;
  transition: all 0.3s ease;
}

/* Row indicator for PHOTOS 5 template */
.photo-row-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(100, 255, 150, 0.8);
  color: black;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
  z-index: 15;
  opacity: 0.8;
  transition: all 0.3s ease;
}

/* Grid position indicator for PHOTOS 5 template */
.photo-grid-position {
  position: absolute;
  bottom: 12px;
  right: 12px;
  background: rgba(100, 255, 150, 0.6);
  color: black;
  padding: 3px 6px;
  border-radius: 3px;
  font-size: 9px;
  font-weight: bold;
  z-index: 15;
  opacity: 0.7;
  transition: all 0.3s ease;
}

/* Image for PHOTOS 5 template */
.photo-image-photos5 {
  width: 100%;
  height: 134px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.photo-frame-photos5:hover .photo-image-photos5 {
  transform: scale(1.02);
}

/* Content container for PHOTOS 5 template */
.photo-content-photos5 {
  padding: 16px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%);
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Title for PHOTOS 5 template */
.photo-title-photos5 {
  color: white;
  font-size: 14px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 8px;
}

/* Description for PHOTOS 5 template */
.photo-description-photos5 {
  color: rgba(255, 255, 255, 0.8);
  font-size: 11px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.3;
}

/* Hover effects for PHOTOS 5 template */
.photo-frame-photos5:hover .photo-number-overlay-photos5,
.photo-frame-photos5:hover .photo-row-indicator,
.photo-frame-photos5:hover .photo-grid-position {
  opacity: 1;
  transform: scale(1.05);
}

.photo-frame-photos5:hover .photo-number-overlay-photos5 {
  background: rgba(0, 0, 0, 0.95);
  border-color: rgba(100, 255, 150, 0.5);
}

/* Focus mode styles for PHOTOS 5 template */
.photo-frame-photos5.focus-fade-out {
  opacity: 0.1 !important;
  transition: opacity 0.4s ease !important;
  cursor: not-allowed !important;
}

.photo-frame-photos5.focus-centered {
  opacity: 1 !important;
  z-index: 1000;
  transition: opacity 0.4s ease !important;
  box-shadow: 0 25px 70px rgba(100, 255, 150, 0.4) !important;
  border-color: rgba(100, 255, 150, 0.9) !important;
  cursor: pointer !important;
}

/* Instant positioning for PHOTOS 5 template */
.photo-frame-photos5.instant-position {
  transition: none !important;
}

.photo-frame-photos5.instant-position * {
  transition: none !important;
}

/* Fly-in animation for PHOTOS 5 template */
.photo-frame-photos5.fly-in-active {
  transition: none !important;
}

/* Layout-specific transition rules for PHOTOS5 RANDOM - PERFORMANCE OPTIMIZED for 60 FPS */
.photo-frame-photos5.layout-random {
  /* PERFORMANCE: Only animate transform for hardware acceleration */
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* Enhanced hardware acceleration for smooth animations */
  transform-style: preserve-3d;
  backface-visibility: hidden;
  /* PERFORMANCE: will-change managed dynamically via JavaScript */
}

/* Layout-specific transition rules for PHOTOS5 RANDOM2 - PERFORMANCE OPTIMIZED for 60 FPS */
.photo-frame-photos5.layout-random2 {
  /* PERFORMANCE: Faster transitions for RANDOM2's more dynamic nature */
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* Enhanced hardware acceleration for smooth animations */
  transform-style: preserve-3d;
  backface-visibility: visible; /* FIXED: Allow backface to be visible during Y-axis rotation */
  /* PERFORMANCE: will-change managed dynamically via JavaScript */
}

/* PERFORMANCE OPTIMIZED RANDOM2 layout styling for PHOTOS5 template - 60 FPS target */
.photo-frame-photos5.layout-random2 {
  /* PERFORMANCE: Distinct styling with purple/green theme for PHOTOS5 */
  border: 2px solid rgba(200, 100, 255, 0.5);
}

/* Enhanced hover effects for RANDOM2 layout in PHOTOS5 template - PERFORMANCE OPTIMIZED for 60 FPS */
.photo-frame-photos5.layout-random2:hover {
  /* PERFORMANCE: Distinct hover effects for RANDOM2 in PHOTOS5 */
  border-color: rgba(200, 100, 255, 0.9);
  background-color: rgba(30, 0, 30, 0.7) !important;
  transform: scale(1.03);
}
</style>
