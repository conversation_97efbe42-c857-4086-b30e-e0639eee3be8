/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-18 14:22:53
 * @LastEditTime: 2025-07-14 15:30:01
 * @LastEditors: liu<PERSON><PERSON>
 */
import axios from "axios";

const axiosInstance = axios.create({
  baseURL: "https://api.yischool.cn/",
});


//作品列表
/**
 * 作品列表
 * @param 
    sid: 92,
    student_id: string, 学生id
    banji: string 班级
 */
export async function get3dEffect(params) {
  return axiosInstance.post("/student/artwork_outstanding_list", params);
}
//班级学生数量
export async function getStudentCount(params) {
  return axiosInstance.post("/student/outstanding_class_list", params);
}

/**
 * 学生列表
 * @param 
    sid: 92,
    campus_id: 105,
 * @returns 
 */

export async function getStudentList(params) {
  return axiosInstance.post("/student/outstanding_student_list", params);
}
/**
 * 年级学生作品分组
 * @param 
    sid: 92,
    campus_id: 105,
    class_id: ,
 * @returns 
 */
export async function getStudentArtworkList(params) {
  return axiosInstance.post(
    "/student/outstanding_student_artwork_list",
    params
  );
}

/**
 * 喜欢点赞
 * @param 
   sid string 学校id 目前92
   campus_id string 校区id 目前105
   if_like string 1喜欢 0点赞
   artwork_id string 文章id
 */

export async function likeArtwork(params) {
  return axiosInstance.post("/student/outstanding_student_artwork_praise", params);
}

