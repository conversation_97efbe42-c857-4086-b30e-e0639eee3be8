<template>
  <!-- PHOTOS3 Template Component - Hidden by default, used for cloning -->
  <div id="photo-frame-template-photos3" class="photo-frame-photos3" style="display: none" ref="templateRef">
    <img class="photo-image" alt="" />
    <div class="photo-frame-photos3Text">
      <div class="photo-title"></div>
      <div class="photo-description"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineExpose } from "vue";

// Template reference for external access
const templateRef = ref(null);

// Props for component configuration
const props = defineProps({
  // Allow customization of template ID if needed
  templateId: {
    type: String,
    default: "photo-frame-template-photos3",
  },
  // Allow visibility control from parent
  visible: {
    type: <PERSON>olean,
    default: false,
  },
});

// Update template ID and visibility when component mounts
onMounted(() => {
  if (templateRef.value) {
    // Set custom template ID if provided
    if (props.templateId !== "photo-frame-template-photos3") {
      templateRef.value.id = props.templateId;
    }

    // Update visibility based on props
    if (props.visible) {
      templateRef.value.style.display = "";
    }
  } else {
  }
});

// Expose template reference and utility methods for parent access
defineExpose({
  templateRef,
  getTemplateElement: () => templateRef.value,
  setVisible: (visible) => {
    if (templateRef.value) {
      templateRef.value.style.display = visible ? "" : "none";
    }
  },
  getId: () => templateRef.value?.id || props.templateId,
});
</script>

<style scoped>
/* ===== PHOTOS 3 TEMPLATE STYLES ===== */
.photo-frame-photos3 {
  width: 399px;
  height: 322px;
  cursor: pointer;
  /* Default: smooth transitions for hover effects only */
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
  overflow: hidden;
  backdrop-filter: blur(10px);
  /* Optimize for 3D transforms - enables hardware acceleration */
  will-change: transform;

  background-image: url("../../public/n.png");
  background-repeat: no-repeat;
  background-size: contain;

  display: flex;
  flex-direction: column;
  align-items: center;
}
.photo-frame-photos3nu {
  background-image: url("../../public/nu.png");
}
.photo-frame-photos3Text {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 15px;
  height: 87px;
  font-size: 33px;
}
/* Layout-specific transition rules for PHOTOS3 - PERFORMANCE OPTIMIZED for 60 FPS */
.photo-frame-photos3.layout-random {
  /* PERFORMANCE: Only animate transform for hardware acceleration */
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* Enhanced hardware acceleration for smooth animations */
  transform-style: preserve-3d;
  backface-visibility: hidden;
  /* PERFORMANCE: will-change managed dynamically via JavaScript */
}

/* Layout-specific transition rules for PHOTOS3 RANDOM2 - PERFORMANCE OPTIMIZED for 60 FPS */
.photo-frame-photos3.layout-random2 {
  /* PERFORMANCE: Faster transitions for RANDOM2's more dynamic nature */
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* Enhanced hardware acceleration for smooth animations */
  transform-style: preserve-3d;
  backface-visibility: visible; /* FIXED: Allow backface to be visible during Y-axis rotation */
  /* PERFORMANCE: will-change managed dynamically via JavaScript */
}

.photo-frame-photos3.layout-grid {
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
}

.photo-frame-photos3.layout-grid.fly-in-active {
  transition: none !important;
}

.photo-frame-photos3.layout-switching {
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* PERFORMANCE OPTIMIZED random layout styling for PHOTOS3 template - 60 FPS target */
.photo-frame-photos3.layout-random {
  /* PERFORMANCE: Removed expensive box-shadow and backdrop-filter for better performance */
  border: 2px solid rgba(255, 150, 100, 0.4);
}

/* PERFORMANCE OPTIMIZED RANDOM2 layout styling for PHOTOS3 template - 60 FPS target */
.photo-frame-photos3.layout-random2 {
  /* PERFORMANCE: Distinct styling with purple/orange theme for PHOTOS3 */
  border: 2px solid rgba(255, 100, 150, 0.5);
}

/* Enhanced hover effects for RANDOM2 layout in PHOTOS3 template - PERFORMANCE OPTIMIZED for 60 FPS */

.photo-frame-photos3.instant-position {
  transition: none !important;
}

.photo-frame-photos3.instant-position * {
  transition: none !important;
}

/* Focus mode styles - active in RANDOM, RANDOM2 and PHOTOS2 layouts */
.photo-frame-photos3.focus-fade-out {
  opacity: 0.1 !important;
  transition: opacity 0.4s ease !important;
  cursor: not-allowed !important; /* Show not-allowed cursor for non-focused elements */
}

.photo-frame-photos3.focus-centered {
  opacity: 1 !important;
  z-index: 1000; /* Bring focused element to front */
  transition: opacity 0.4s ease !important;
  box-shadow: 0 20px 60px rgba(255, 150, 100, 0.3) !important;
  border-color: rgba(255, 150, 100, 0.8) !important;
  cursor: pointer !important; /* Keep pointer cursor for focused element */
}

/* Child element styles */
.photo-frame-photos3 .photo-number-overlay {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(255, 150, 100, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  z-index: 10;
}

.photo-frame-photos3 .photo-image {
  width: 159px;
  height: 159px;
  margin-top: 44px;
  object-fit: cover;
  border-radius: 90px;
}

.photo-frame-photos3 .photo-title {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.photo-frame-photos3 .photo-description {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  margin-left: 10px;
}
</style>
