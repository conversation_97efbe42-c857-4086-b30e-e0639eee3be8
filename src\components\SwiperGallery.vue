<template>
  <!-- Modal Overlay -->
  <div v-if="isVisible" class="carousel-modal-overlay">
    <!-- Modal Content -->
    <div class="carousel-modal-content" @click.stop>
      <!-- Close Button -->
      <div class="carousel-close-btn" @click="closeModal" title="Close Gallery"></div>

      <!-- Custom Coverflow Carousel -->
      <div class="carousel-container" ref="carouselContainer">
        <div
          class="carousel-track"
          ref="carouselTrack"
          :style="trackStyle"
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
          @mousedown="handleMouseDown"
          @mousemove="handleMouseMove"
          @mouseup="handleMouseUp"
          @mouseleave="handleMouseUp"
          @dragstart.prevent
          @selectstart.prevent
          @contextmenu.prevent
        >
          <div
            v-for="(photo, index) in photoDataset"
            :key="photo.id || index"
            class="carousel-slide"
            :class="{
              active: index === currentIndex,
              prev: index < currentIndex,
              next: index > currentIndex,
            }"
            :style="getSlideStyle(index)"
            @click="handleSlideClick(index, photo)"
          >
            <div class="carousel-slide-content">
              <div class="dl"></div>
              <img :src="photo.src" :alt="photo.title" draggable="false" @dragstart.prevent @selectstart.prevent @error="handleImageError" @load="handleImageLoad" />
              <div class="carousel-slide-info">
                <div class="carousel1">{{ photo.title }}</div>
                <div class="carousel2" v-html="formattext(photo.description)"></div>
                <div class="carousel3">
                  <div class="carousel31" @click.stop="handleZan('like', photo)">
                    <div class="carousel31Icon"></div>
                    <div class="carousel31Text">{{ photo.zan }}</div>
                  </div>
                  <div class="carousel31 carousel32" @click.stop="handleZan('praise', photo)">
                    <div class="carousel31Icon carousel32Icon"></div>
                    <div class="carousel31Text carousel32Text">{{ photo.ax }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div class="carousel-pagination">
          <button
            v-for="(photo, index) in photoDataset"
            :key="`pagination-${photo.id || index}`"
            class="carousel-pagination-dot"
            :class="{ active: index === currentIndex }"
            @click="goToSlide(index)"
          ></button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from "vue";
import { likeArtwork } from "../api/index";

// Props
const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false,
  },
  photoDataset: {
    type: Array,
    default: () => [],
  },
});

function formattext(value) {
  const text = value.replace("-", "&nbsp;&nbsp;&nbsp;&nbsp;");
  return text;
}

const handleZan = (type, photo) => {
  console.log("🎯 handleZan triggered! type:", type, "photo:", photo);
  // if (type == "like") {
    likeArtwork({ sid: 92, campus_id: 105, artwork_id: photo.id, if_like: type == "like" ? 1 : 0 }).then((res) => {
      console.log("res", res);
      if (res.data.code == 200) {
        if (type == "like") {
          photo.zan = parseInt(photo.zan) + 1;
        } else {
          photo.ax = parseInt(photo.ax) + 1;
        }
      }
    });
  // }
};

// Emits
const emit = defineEmits(["slide-click", "carousel-ready", "carousel-error", "close-modal"]);

// Refs
const carouselContainer = ref(null);
const carouselTrack = ref(null);

// Reactive state
const currentIndex = ref(0);
const isTransitioning = ref(false);
const isDragging = ref(false);
const startX = ref(0);
const currentX = ref(0);
const dragOffset = ref(0);

// Carousel configuration
const config = {
  slideWidth: 300,
  slideHeight: 400,
  spacing: 50,
  rotateAngle: 50,
  scaleInactive: 0.8,
  depthOffset: 100,
  transitionDuration: 600,
  swipeThreshold: 50,
};

// Computed properties
const trackStyle = computed(() => {
  if (isDragging.value) {
    return {
      transform: `translateX(${dragOffset.value}px)`,
      transition: "none",
    };
  }
  return {
    transform: `translateX(0px)`,
    transition: `transform ${config.transitionDuration}ms cubic-bezier(0.25, 0.46, 0.45, 0.94)`,
  };
});

// Methods
const getSlideStyle = (index) => {
  const offset = index - currentIndex.value;
  const absOffset = Math.abs(offset);

  // Only show 3 elements: current (0), previous (-1), and next (+1)
  // Hide all other elements by setting them completely out of view
  if (absOffset > 1) {
    return {
      transform: `translateX(${offset > 0 ? 2000 : -2000}px) translateZ(-1000px) scale(0)`,
      opacity: 0,
      zIndex: -1,
      transition: isDragging.value ? "none" : `all ${config.transitionDuration}ms cubic-bezier(0.25, 0.46, 0.45, 0.94)`,
      pointerEvents: "none",
    };
  }

  // Base positioning for visible elements (only 3 elements)
  const translateX = offset * (config.slideWidth + config.spacing);
  let translateZ = 0;
  let rotateY = 0;
  let scale = 1;
  let opacity = 1;

  if (offset !== 0) {
    // Side slides - create coverflow effect
    rotateY = offset > 0 ? -config.rotateAngle : config.rotateAngle;
    translateZ = -config.depthOffset * absOffset;
    scale = config.scaleInactive;
    opacity = 0.8; // Keep side elements clearly visible
  }

  // Add drag offset if dragging
  const dragAdjustment = isDragging.value ? dragOffset.value : 0;

  return {
    transform: `translateX(${translateX + dragAdjustment}px) translateZ(${translateZ}px) rotateY(${rotateY}deg) scale(${scale})`,
    opacity: opacity,
    zIndex: 100 - absOffset,
    transition: isDragging.value ? "none" : `all ${config.transitionDuration}ms cubic-bezier(0.25, 0.46, 0.45, 0.94)`,
    pointerEvents: "auto",
  };
};

// Navigation methods
const goToSlide = (index) => {
  if (index >= 0 && index < props.photoDataset.length && index !== currentIndex.value) {
    isTransitioning.value = true;
    currentIndex.value = index;

    setTimeout(() => {
      isTransitioning.value = false;
    }, config.transitionDuration);
  }
};

const nextSlide = () => {
  const nextIndex = (currentIndex.value + 1) % props.photoDataset.length;
  goToSlide(nextIndex);
};

const prevSlide = () => {
  const prevIndex = currentIndex.value === 0 ? props.photoDataset.length - 1 : currentIndex.value - 1;
  goToSlide(prevIndex);
};

const handleSlideClick = (index, photo) => {
  console.log("🚀 ~ handleSlideClick ~ index, photo:", index, photo);
  if (index === currentIndex.value) {
    // Clicked on center slide - emit click event
    emit("slide-click", { index, photo });
  } else {
    // Clicked on side slide - navigate to it
    goToSlide(index);
  }
};

// Touch and mouse event handlers
const handleTouchStart = (event) => {
  if (isTransitioning.value) return;

  // Prevent default behaviors that could interfere with our custom handling
  event.preventDefault();
  event.stopPropagation();

  isDragging.value = true;
  startX.value = event.touches ? event.touches[0].clientX : event.clientX;
  currentX.value = startX.value;
  dragOffset.value = 0;
};

const handleTouchMove = (event) => {
  if (!isDragging.value || isTransitioning.value) return;

  // Prevent default scrolling and other browser behaviors
  event.preventDefault();
  event.stopPropagation();

  currentX.value = event.touches ? event.touches[0].clientX : event.clientX;
  dragOffset.value = currentX.value - startX.value;
};

const handleTouchEnd = (event) => {
  if (!isDragging.value) return;

  // Prevent default behaviors
  event.preventDefault();
  event.stopPropagation();

  const deltaX = currentX.value - startX.value;
  const absDelta = Math.abs(deltaX);

  if (absDelta > config.swipeThreshold) {
    if (deltaX > 0) {
      // Swiped right - go to previous slide
      prevSlide();
    } else {
      // Swiped left - go to next slide
      nextSlide();
    }
  }

  // Reset drag state
  isDragging.value = false;
  dragOffset.value = 0;
  startX.value = 0;
  currentX.value = 0;
};

// Mouse event handlers (for desktop)
const handleMouseDown = (event) => {
  // Prevent default mouse behaviors like text selection and image dragging
  event.preventDefault();
  event.stopPropagation();
  handleTouchStart(event);
};

const handleMouseMove = (event) => {
  if (isDragging.value) {
    event.preventDefault();
    event.stopPropagation();
  }
  handleTouchMove(event);
};

const handleMouseUp = (event) => {
  if (isDragging.value) {
    event.preventDefault();
    event.stopPropagation();
  }
  handleTouchEnd(event);
};

// Keyboard event handler
const handleKeyDown = (event) => {
  if (event.key === "ArrowLeft") {
    event.preventDefault();
    prevSlide();
  } else if (event.key === "ArrowRight") {
    event.preventDefault();
    nextSlide();
  }
};

// Initialize carousel
const initializeCarousel = () => {
  // Reset to first slide
  currentIndex.value = 0;
  isTransitioning.value = false;
  isDragging.value = false;

  emit("carousel-ready", { slideCount: props.photoDataset.length });
};

// Image loading handlers
const handleImageError = (event) => {
  console.error("🎠 Image failed to load:", event.target.src);
  emit("carousel-error", { error: "Image failed to load", src: event.target.src });
};

const handleImageLoad = (event) => {
  // Image loaded successfully
};

// Modal functionality
const closeModal = () => {
  emit("close-modal");
};

// Watchers
watch(
  () => props.isVisible,
  (newVisible) => {
    if (newVisible && props.photoDataset.length > 0) {
      nextTick(() => {
        initializeCarousel();
      });
    }
  }
);

watch(
  () => props.photoDataset,
  () => {
    if (props.isVisible && props.photoDataset.length > 0) {
      initializeCarousel();
    }
  },
  { deep: true }
);

// Lifecycle
onMounted(() => {
  if (props.isVisible && props.photoDataset.length > 0) {
    initializeCarousel();
  }

  // Add global event listeners
  document.addEventListener("keydown", handleKeyDown);
  document.addEventListener("mousemove", handleMouseMove);
  document.addEventListener("mouseup", handleMouseUp);

  // Prevent default drag behaviors globally when carousel is active
  document.addEventListener("dragstart", (e) => {
    if (props.isVisible) {
      e.preventDefault();
    }
  });

  document.addEventListener("selectstart", (e) => {
    if (props.isVisible && isDragging.value) {
      e.preventDefault();
    }
  });
});

onUnmounted(() => {
  // Remove global event listeners
  document.removeEventListener("keydown", handleKeyDown);
  document.removeEventListener("mousemove", handleMouseMove);
  document.removeEventListener("mouseup", handleMouseUp);
});

// Expose methods for parent component
defineExpose({
  initializeCarousel,
  goToSlide,
  nextSlide,
  prevSlide,
  currentIndex,
});
</script>

<style lang="less" scoped>
/* Modal Overlay Styles */
.carousel-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.carousel-modal-content {
  position: relative;
  width: 57vw;
  height: 76vh;
  max-width: 1200px;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
  border-radius: 20px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.8);
  overflow: hidden;
  animation: slideIn 0.4s ease-out;
}

.carousel-close-btn {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  color: white;
  cursor: pointer;
  z-index: 10001;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);

  background-image: url("../../public/cha.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;

  &:hover {
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }

  svg {
    width: 20px;
    height: 20px;
  }
}

/* Carousel Container Styles */
.carousel-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background-image: url("../../w.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.carousel-track {
  /* Limit width to show only 3 elements: center + left + right */
  width: calc(300px * 3 + 50px * 2); /* 3 slides + 2 gaps */
  max-width: 100%;
  height: calc(100% - 80px); /* Account for pagination */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  perspective: 1200px;
  perspective-origin: center center;
  transform-style: preserve-3d;
  cursor: grab;
  touch-action: pan-x;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  overflow: hidden; /* Ensure elements outside viewport are hidden */

  &:active {
    cursor: grabbing;
  }
}

.carousel-slide {
  position: absolute;
  width: 408px;
  height: 545px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-style: preserve-3d;
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  pointer-events: auto;
  /* Optimize for 3D transforms - enables hardware acceleration */
  will-change: transform;

  &.active {
    z-index: 100;
  }

  &.prev,
  &.next {
    z-index: 50;
  }
}

/* Carousel Slide Content Styles */
.carousel-slide-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  /* Optimize for 3D transforms - enables hardware acceleration */
  will-change: transform;

  background-image: url("../../t.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;

  display: flex;
  flex-direction: column;
  align-items: center;
}

.carousel-slide-content img {
  width: 77%;
  margin-top: 30px;
  height: 64.5%;
  object-fit: cover;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  pointer-events: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

.carousel-slide-info {
  width: 61%;
  height: 30%;
  display: flex;
  flex-direction: column;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  pointer-events: none;
}
.carousel1 {
  margin-top: 20px;
  font-size: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  letter-spacing: 2px;
  font-weight: bold;
  color: #4d2f00;
}
.carousel2 {
  margin-top: 13px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 19px;
  color: #593e0f;
}
.carousel3 {
  margin-top: 5px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
}
.carousel31 {
  width: 65px;
  height: 33px;
  background-image: url("../../public/a41.png");
  background-repeat: no-repeat;
  background-size: contain;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: auto;
  cursor: pointer;
}
.carousel31Icon {
  width: 20px;
  height: 20px;
  background-image: url("../../public/a2.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.carousel32Icon {
  background-image: url("../../public/a3.png");
}
.carousel31Text {
  margin-left: 1px;
  color: #b52400;
  font-size: 15px;
}
.carousel32Text {
  color: #b56700;
}
.carousel32 {
  margin-left: 20px;
  background-image: url("../../public/a4.png");
}

.carousel-slide-title {
  font-size: 16px;
  font-weight: bold;
  color: white;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
  margin: 0 0 8px 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.carousel-slide-description {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
  margin: 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.carousel-slide-category {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(100, 150, 255, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Carousel Pagination Styles */
.carousel-pagination {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  z-index: 10001;
}

.carousel-pagination-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.7;

  &:hover {
    background: rgba(255, 255, 255, 0.7);
    transform: scale(1.1);
  }

  &.active {
    background: rgba(255, 215, 0, 0.9);
    opacity: 1;
    transform: scale(1.2);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  }
}

/* Active Center Slide Highlight Effect */
.carousel-slide.active .carousel-slide-content {
  transform: scale(1.03) !important;
  z-index: 5 !important;
}

.carousel-slide.active .carousel-slide-title {
  color: rgba(255, 215, 0, 1) !important;
  text-shadow: 0 0 8px rgba(255, 215, 0, 0.4) !important;
}

.carousel-slide.active .carousel-slide-info {
}

/* Modal Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .carousel-modal-content {
    width: 95vw;
    height: 90vh;
    border-radius: 15px;
  }

  .carousel-close-btn {
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;

    svg {
      width: 18px;
      height: 18px;
    }
  }

  .carousel-container {
    padding: 15px;
  }

  .carousel-track {
    /* Limit width for mobile to show only 3 elements */
    width: calc(250px * 3 + 50px * 2); /* 3 slides + 2 gaps (keep same spacing) */
    max-width: 100%;
  }

  .carousel-slide {
    width: 250px;
    height: 350px;
  }

  .carousel-slide-content {
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .carousel-slide-content img {
  }

  .carousel-slide-info {
    padding: 12px;
  }

  .carousel-slide-title {
    font-size: 14px;
  }

  .carousel-slide-description {
    font-size: 12px;
  }

  .carousel-pagination-dot {
    width: 10px;
    height: 10px;
  }
}
.dl {
  position: absolute;
  top: 0;
  right: 0;
  width: 76px;
  height: 100px;
  background-image: url("../../public/d.png");
  background-repeat: no-repeat;
  background-size: contain;
  z-index: 100;
}
</style>
